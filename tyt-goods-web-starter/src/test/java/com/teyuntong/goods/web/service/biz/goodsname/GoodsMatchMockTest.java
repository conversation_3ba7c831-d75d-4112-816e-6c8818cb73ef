package com.teyuntong.goods.web.service.biz.goodsname;

import com.teyuntong.goods.web.TestBase;
import com.teyuntong.goods.web.starter.TytGoodsWebApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * 货源小程序测试类,直接调用controller
 *
 * <AUTHOR>
 * @since 2023-11-02 23:30
 */

@Slf4j
@Disabled
class GoodsMatchMockTest extends TestBase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Test
    void goodsMatch() throws Exception {
        //初始化MockMvc
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        //调用接口
        mockMvc.perform(MockMvcRequestBuilders.get("/goods/match/list?keywords=挖掘机&pageNum=1&pageSize=25"))
                .andExpect(status().isOk())
                .andDo(print()) // 打印请求和响应的详细信息
                .andReturn()
                .getResponse()
                .getContentAsString();
    }

}