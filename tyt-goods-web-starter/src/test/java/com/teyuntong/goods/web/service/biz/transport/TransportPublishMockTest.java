package com.teyuntong.goods.web.service.biz.transport;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.web.service.biz.callphonerecord.service.CallPhoneRecordService;
import com.teyuntong.goods.web.service.biz.quotedprice.service.TransportQuotedPriceService;
import com.teyuntong.goods.web.starter.TytGoodsWebApplication;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.Base64Utils;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * 货源小程序测试类,直接调用controller
 *
 * <AUTHOR>
 * @since 2023-11-02 23:30
 */
@SpringBootTest(classes = TytGoodsWebApplication.class)
@Slf4j
@Disabled
class TransportPublishMockTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;

    @Autowired
    private CallPhoneRecordService callPhoneRecordService;

    @Test
    void query() throws Exception {
        //初始化MockMvc
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        PublishDTO publishDTO = new PublishDTO();
//        publishDTO.setRefundFlag(0);
//        publishDTO.setStartCoordX(new BigDecimal(194.35).setScale(2, BigDecimal.ROUND_HALF_UP));
//        publishDTO.setStartCoordY(new BigDecimal(3855.72).setScale(2, BigDecimal.ROUND_HALF_UP));
//        publishDTO.setDestCoordX(new BigDecimal(108.98).setScale(2, BigDecimal.ROUND_HALF_UP));
//        publishDTO.setDestCoordY(new BigDecimal(4180.19).setScale(2, BigDecimal.ROUND_HALF_UP));
//        publishDTO.setStartProvinc("河南省");
//        publishDTO.setStartCity("郑州市");
//        publishDTO.setStartArea("金水区");
//        publishDTO.setStartDetailAdd("金水区 京广快速路入口与沙口路交叉口");
//        publishDTO.setStartLongitude(new BigDecimal("113.623573"));
//        publishDTO.setStartLatitude(new BigDecimal("34.795567"));
//        publishDTO.setTel("13783587332");
//        publishDTO.setDestPoint("太原市小店区");
//        publishDTO.setDestProvinc("山西省");
//        publishDTO.setDestCity("太原市");
//        publishDTO.setDestArea("小店区");
//        publishDTO.setDestDetailAdd("许东路与许坦东街交叉口东南100米(锦东国际商务中心4层) 北营街道办事处");
//        publishDTO.setDestLongitude(new BigDecimal("112.604258"));
//        publishDTO.setDestLatitude(new BigDecimal("37.800946"));
//        publishDTO.setTyreExposedFlag("0");
//        publishDTO.setTaskContent("好好挖掘eee机123334");
//        publishDTO.setShuntingQuantity(1);
//        publishDTO.setFixPriceMax(6000);
//        publishDTO.setIsAutoResend(0);
//        publishDTO.setInfoFee(new BigDecimal(200));
//        publishDTO.setExcellentGoods(0);
//        publishDTO.setSourceType(0);
//        publishDTO.setDistance(new BigDecimal(434));
//        publishDTO.setWeight("20");
//        publishDTO.setLength("3");
//        publishDTO.setInvoiceTransport(0);
//        publishDTO.setPublishType(1);
//        publishDTO.setLoadingTime(DateUtil.date(0));

        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setUserId(1002000780L);
        loginUserDTO.setUserName("哈哈");
        loginUserDTO.setCtime(DateUtil.offsetDay(new Date(), 33));

        publishDTO.setCargoOwnerId(0L);
        publishDTO.setClimb("0");
        publishDTO.setDestAddrSource(0);
        publishDTO.setDestDetailAdd("黄河二十路与渤海四路交汇处 滨州站(暂停营业)");
        publishDTO.setDestArea("滨城区");
        publishDTO.setDestCity("滨州市");
        publishDTO.setDestLatitude(new BigDecimal("37.453306"));
        publishDTO.setDestLongitude(new BigDecimal("118.018319"));
        publishDTO.setDestPoint("山东省滨州市滨城区");
        publishDTO.setDestProvinc("山东省");
        publishDTO.setDistance(new BigDecimal(360));
        publishDTO.setDriverDriving(0);
        publishDTO.setExcellentGoods(1);
        publishDTO.setFixPriceFast(5000);
        publishDTO.setFixPriceMin(4500);
        publishDTO.setGoodCarPriceTransport(1);
        publishDTO.setGuaranteeGoods(0);
        publishDTO.setHigh("3.5");
        publishDTO.setInfoFee(new BigDecimal(100));
        publishDTO.setInvoiceTransport(0);
        publishDTO.setIsAutoResend(1);
        publishDTO.setIsBackendTransport(0);
        publishDTO.setLength("10");
        publishDTO.setMatchItemId(200017);
        publishDTO.setPrice("4500");
        publishDTO.setPriorityRecommend(1);
        publishDTO.setPublishTransportIsShowGoodCar(1);
        publishDTO.setPublishType(2);
        publishDTO.setRefundFlag(1);
        publishDTO.setShowGoodCarPriceTransportTab(1);
        publishDTO.setShuntingQuantity(1);
        publishDTO.setStartAddrSource(0);
        publishDTO.setStartDetailAdd("中关村大街29号 (海淀黄庄地铁站A1西北口步行130米) 北京市海淀医院");
        publishDTO.setStartAddrSource(0);
        publishDTO.setStartArea("海淀区");
        publishDTO.setStartCity("北京市");
        publishDTO.setStartLongitude(new BigDecimal("116.315102"));
        publishDTO.setStartLatitude(new BigDecimal("39.977058"));
        publishDTO.setStartPoint("北京市海淀区");
        publishDTO.setStartProvinc("北京市");
        publishDTO.setSuggestMaxPrice(5500);
        publishDTO.setSuggestMinPrice(4400);
        publishDTO.setSuggestPrice(4931);
        publishDTO.setTaskContent("360装载机额ffvf");
       publishDTO.setTel("13783587332");
        publishDTO.setThMaxPrice(55000);
        publishDTO.setThMinPrice(44000);
        publishDTO.setSrcMsgId(88823474L);
        publishDTO.setWeight("35.5");



        //调用接口
        mockMvc.perform(post("/goods/publish/saveTransport")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .header("x-app-user-id", "1002000780")
                        .header("x-app-login-user", Base64Utils.encodeToString(JSONUtil.toJsonStr(loginUserDTO).getBytes()))

                        .content(JSONUtil.toJsonStr(publishDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

}