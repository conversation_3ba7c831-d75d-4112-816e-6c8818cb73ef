package com.teyuntong.goods.web.service.biz.transport;

import com.teyuntong.goods.web.service.biz.callphonerecord.dto.CallPhoneRecordAddRemarkReq;
import com.teyuntong.goods.web.service.biz.callphonerecord.service.CallPhoneRecordService;
import com.teyuntong.goods.web.service.biz.quotedprice.service.TransportQuotedPriceService;
import com.teyuntong.goods.web.starter.TytGoodsWebApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;


/**
 * 货源小程序测试类,直接调用controller
 *
 * <AUTHOR>
 * @since 2023-11-02 23:30
 */
@SpringBootTest(classes = TytGoodsWebApplication.class)
@Slf4j
@Disabled
class TransportBackendMockTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;

    @Autowired
    private CallPhoneRecordService callPhoneRecordService;

    @Test
    void query() throws Exception {
        //初始化MockMvc
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        //调用接口
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/transport/backend/query")
                        .accept(MediaType.APPLICATION_JSON_UTF8_VALUE)
                        .param("srcMsgId", "33803263"))
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }

    @Test
    void queryBySrcMsgId() throws Exception {
        //初始化MockMvc
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        //调用接口
        String result = mockMvc.perform(MockMvcRequestBuilders.get("/transport/backend/queryBySrcMsgId")
                        .accept(MediaType.APPLICATION_JSON_UTF8_VALUE)
//                        .param("srcMsgId2", "33803263"))
                        .param("srcMsgId", "0"))
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }

    @Test
    public void test4324() {
        int transportNoLookOverQuotedPriceCountNum = transportQuotedPriceService.getTransportNoLookOverQuotedPriceCountNum(10001L);
        System.out.println(transportNoLookOverQuotedPriceCountNum);
    }


    @Test
    public void test4324234() {
        Integer transportNoLookCallLogCountNum = callPhoneRecordService.getTransportNoLookCallLogCountNum(1000002552L);
        System.out.println(transportNoLookCallLogCountNum);
    }

    @Test
    public void test43234() {
        callPhoneRecordService.addRemark(new CallPhoneRecordAddRemarkReq(10001L, 22222L, "测试"));
    }

}