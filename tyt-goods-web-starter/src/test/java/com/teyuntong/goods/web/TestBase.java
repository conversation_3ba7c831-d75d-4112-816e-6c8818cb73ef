package com.teyuntong.goods.web;

import com.teyuntong.goods.web.starter.TytGoodsWebApplication;
import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;

@AutoConfigureMockMvc
@SpringBootTest(classes = TytGoodsWebApplication.class)
@ContextConfiguration
@Disabled
public class TestBase {

    @Autowired
    protected MockMvc mockMvc;
}
