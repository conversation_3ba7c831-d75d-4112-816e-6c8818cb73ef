package com.teyuntong.goods.web.starter;

import com.teyuntong.infra.common.retrofit.core.EnableRetrofitClient;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableFeignClients(basePackages = "com.teyuntong")
@MapperScan(basePackages = "com.teyuntong", annotationClass = Mapper.class)
@SpringBootApplication(scanBasePackages = "com.teyuntong")
@EnableRetrofitClient(basePackages = "com.teyuntong")
public class TytGoodsWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(TytGoodsWebApplication.class, args);
    }

}