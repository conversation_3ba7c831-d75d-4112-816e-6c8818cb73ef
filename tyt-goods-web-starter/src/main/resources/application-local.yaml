spring:
  datasource:
#    url: '***********************************************************************************************************************************************'
#    username: tyt_dev
#    password: tyt_dev#20200724
    url: '***********************************************************************************************************************************************'
    username: test_readonly
    password: test_readonly@TE
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 40
      minimum-idle: 15
      max-lifetime: 30000
  redis:
#    database: 0
#    host: public-network-dev-0.redis.rds.aliyuncs.com
#    port: 16379
#    password: TyT@dev#20220323
    database: 2
    host: r-2zehe8qbmlx8071kcppd.redis.rds.aliyuncs.com
    port: 6379
    password: tyt_Test_redis0608
    lettuce:
      pool:
        # 最大连接数
        max-active: 8
        # 最大空闲连接数
        max-idle: 8
        # 最小空闲连接数
        min-idle: 2
  cache:
    redis:
      # 默认缓存失效时间, 30*60*1000 = 30分钟
      timeToLive: 1800000
      keyPrefix: "tytGoodsWebCache:"
custom:
  feign:
    decoder:
      log-business-exception: true
      throw-business-exception: true
mybatis-plus:
  configuration:
    auto-mapping-behavior: full
  mapper-locations: classpath:mapper/*.xml
retrofit:
  circuitbreaker:
    resilience4j:
      enable: true
resilience4j:
  circuitbreaker:
    configs:
      default:
        minimumNumberOfCalls: 10
        slidingWindowSize: 60
        failureRateThreshold: 50
  timelimiter:
    configs:
      default:
        timeoutDuration: 300s
xxl-job:
  enable: false
rocket-mq:
  consumer:
    enable: false
  producer:
    enable: false
easy-es:
#  address : es-cn-jte3nts0v000uamhq.public.elasticsearch.aliyuncs.com:9200 # es的连接地址,必须含端口 若为集群,则可以用逗号隔开 例如:127.0.0.1:9200,*********:9200
  address : ************:9200 # es的连接地址,必须含端口 若为集群,则可以用逗号隔开 例如:127.0.0.1:9200,*********:9200
#  username: elastic #如果无账号密码则可不配置此行
#  password: Es#Tyt@20210524 #如果无账号密码则可不配置此行
  keep-alive-millis: 300000 # 心跳策略时间 单位:ms
  connect-timeout: 10000 # 连接超时时间 单位:ms
  socket-timeout: 20000 # 通信超时时间 单位:ms
  connection-request-timeout: 5000 # 连接请求超时时间 单位:ms
  max-conn-total: 1024 # 最大连接数 单位:个
  max-conn-per-route: 500 # 最大连接路由数 单位:个
  global-config:
    db-config:
      index-prefix: dev_