<?xml version="1.0" encoding="UTF-8"?>
<!--
    scan: 当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。
    scanPeriod: 设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。
    debug: 当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。
    configuration 子节点为 appender、logger、root
-->
<configuration scan="true" scanPeriod="600 seconds" debug="false">
    <!--配置多环境日志输出
    可以在application.properties中配置选择哪个profiles : spring.profiles.active=dev-->
    <!-- <springProfile name="online">
        <property name="print_min_level" value="INFO" />
    </springProfile> -->
    <!-- 打印的最低级别 -->
    <springProperty scope="context" name="print_min_level" source="logging.level.root"/>
    <!-- 项目名称 -->
    <springProperty scope="context" name="app_name" source="spring.application.name"/>
    <!--定义日志文件的存储地址
    勿在 LogBack 的配置中使用相对路径-->
    <springProperty scope="context" name="log_out_path" source="logging.file.path"/>

    <contextName>${HOSTNAME}</contextName>
    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度
    %logger输出日志的logger名 %msg：日志消息，%n是换行符 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr"
                    converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- 彩色日志format -->
    <property name="color_layout_pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %clr(${PID:- }){magenta} %clr(%5p) %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n %ex"/>
    <!-- 正常日志format -->
    <property name="normal_layout_pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} ${PID:- } %5p --- [%15.15t] %-40.40logger{39} : %m%n %ex"/>

    <!--控制台-->
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${color_layout_pattern}</pattern>
            <!--解决乱码问题-->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--滚动文件-->
    <appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log_out_path:-logs}/info/${app_name}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_out_path:-logs}/info/${app_name}-%d{yyyyMMdd}-%i.log</fileNamePattern>
            <!-- 单个日志文件最大200M，到了这个值，就会再创建一个日志文件 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 保存最近7天的日志 -->
            <maxHistory>7</maxHistory>
            <!-- 所有的日志文件最大20G，超过就会删除旧的日志 -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>${normal_layout_pattern}</pattern>
        </encoder>
    </appender>

    <!--滚动文件-->
    <appender name="warnFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log_out_path:-logs}/warn/${app_name}.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_out_path:-logs}/warn/${app_name}-%d{yyyyMMdd}-%i.log</fileNamePattern>
            <!-- 单个日志文件最大200M，到了这个值，就会再创建一个日志文件 -->
            <maxFileSize>200MB</maxFileSize>
            <!-- 保存最近7天的日志 -->
            <maxHistory>7</maxHistory>
            <!-- 所有的日志文件最大20G，超过就会删除旧的日志 -->
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>${normal_layout_pattern}</pattern>
        </encoder>
    </appender>

    <!--滚动文件-->
    <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log_out_path:-logs}/error/${app_name}.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_out_path:-logs}/error/${app_name}-%d{yyyyMMdd}-%i.log</fileNamePattern>
            <!-- 单个日志文件最大200M，到了这个值，就会再创建一个日志文件 -->
            <maxFileSize>200MB</maxFileSize>
            <!-- 保存最近7天的日志 -->
            <maxHistory>7</maxHistory>
            <!-- 所有的日志文件最大20G，超过就会删除旧的日志 -->
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>${normal_layout_pattern}</pattern>
        </encoder>
    </appender>

    <!--什么都不干-->
    <appender name="no_op" class="ch.qos.logback.core.helpers.NOPAppender"/>

    <!-- 把日志异步输出到磁盘文件中，避免每次都进行磁盘IO操作 -->
    <appender name="asyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>-1</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>2048</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="infoFile"/>
    </appender>

    <root level="${logging.root.level}">
        <appender-ref ref="stdout"/>
        <appender-ref ref="asyncAppender"/>
        <appender-ref ref="errorFile"/>
        <appender-ref ref="warnFile"/>
    </root>

    <!--  mq 消费者 logger-->
    <logger name="mq_consumer" level="${logging.root.level}" additivity="false">
        <appender-ref ref="no_op"/>
    </logger>
    <!--  mq 生产者 logger-->
    <logger name="mq_producer" level="${logging.root.level}" additivity="false">
        <appender-ref ref="no_op"/>
    </logger>

</configuration>