package com.teyuntong.goods.web.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.tytconfig.service.ConfigRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "configRemoteService",
        fallbackFactory = ConfigRemoteService.TytConfigRemoteServiceFallback.class)
public interface ConfigRemoteService extends ConfigRpcService {

    @Component
    class TytConfigRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<ConfigRemoteService> {
        public TytConfigRemoteServiceFallback() {
            super(true, ConfigRemoteService.class);
        }
    }
}
