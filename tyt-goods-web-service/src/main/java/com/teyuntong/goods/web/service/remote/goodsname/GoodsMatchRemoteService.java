package com.teyuntong.goods.web.service.remote.goodsname;

import com.teyuntong.goods.service.client.goodsname.service.GoodsMatchRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/5/13 9:39
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "GoodsMatchRpcService", fallbackFactory = GoodsMatchRemoteService.GoodsMatchRemoteFallbackFactory.class)

public interface GoodsMatchRemoteService extends GoodsMatchRpcService {

    @Slf4j
    @Component
     class GoodsMatchRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<GoodsMatchRpcService> {
        protected GoodsMatchRemoteFallbackFactory() {
            super(true, GoodsMatchRpcService.class);
        }


    }
}
