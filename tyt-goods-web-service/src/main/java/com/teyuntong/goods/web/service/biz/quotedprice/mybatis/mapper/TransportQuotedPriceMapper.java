package com.teyuntong.goods.web.service.biz.quotedprice.mybatis.mapper;

import com.teyuntong.goods.web.service.biz.callphonerecord.dto.CallPhoneRecordAddRemarkReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TransportQuotedPriceMapper {

    List<Long> getPublishIngTransportBySrcMsgId(@Param("srcMsgIdList") List<Long> srcMsgIdList);

    void callPhoneRecordDeleteRemark(@Param("srcMsgId") Long srcMsgId, @Param("carUserId") Long carUserId);

    void callPhoneRecordAddRemark(@Param("srcMsgId") Long srcMsgId, @Param("carUserId") Long carUserId, @Param("remark") String remark);

}
