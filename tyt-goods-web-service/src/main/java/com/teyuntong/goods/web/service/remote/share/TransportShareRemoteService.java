package com.teyuntong.goods.web.service.remote.share;

import com.teyuntong.goods.service.client.transport.service.TransportShareRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 货源分享
 *
 * <AUTHOR>
 * @since 2025-06-19 15:28
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "TransportShareRpcService", fallbackFactory = TransportShareRemoteService.TransportShareRemoteServiceFallbackFactory.class)
public interface TransportShareRemoteService extends TransportShareRpcService {
    @Component
    @Slf4j
    class TransportShareRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TransportShareRemoteService> {
        protected TransportShareRemoteServiceFallbackFactory() {
            super(true, TransportShareRemoteService.class);
        }
    }
}
