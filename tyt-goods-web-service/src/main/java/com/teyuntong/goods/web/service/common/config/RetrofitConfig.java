package com.teyuntong.goods.web.service.common.config;

import com.teyuntong.infra.common.core.skywalking.SkyWalkingTraceExecutorWrapper;
import com.teyuntong.infra.common.retrofit.circuitbreaker.RetrofitCBCustomizer;
import com.teyuntong.infra.common.retrofit.circuitbreaker.resilience4j.Resilience4JRetrofitCircuitBreakerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @since 2023/10/17 13:25
 */
@Configuration
public class RetrofitConfig {

    /**
     * 使用 skywalking 的 RunnableWrapper 包装 runnable, 使 traceId 能传递到子线程
     */
    @Bean
    public RetrofitCBCustomizer<Resilience4JRetrofitCircuitBreakerFactory> retrofitCBTimeoutThreadPoolCustomizer() {
        return factory -> factory.configExecutor(new SkyWalkingTraceExecutorWrapper(Executors.newCachedThreadPool()));
    }
}
