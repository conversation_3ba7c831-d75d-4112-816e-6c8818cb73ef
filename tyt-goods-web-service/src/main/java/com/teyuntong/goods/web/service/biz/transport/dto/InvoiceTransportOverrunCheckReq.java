package com.teyuntong.goods.web.service.biz.transport.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceTransportOverrunCheckReq {

    /**
     * 开票主体服务商code
     */
    private String serviceProviderCode;

    /**
     * 重量吨位
     */
    private BigDecimal weight;

    /**
     * 距离
     */
    private BigDecimal distance;

    /**
     * 运费
     */
    private BigDecimal price;

}
