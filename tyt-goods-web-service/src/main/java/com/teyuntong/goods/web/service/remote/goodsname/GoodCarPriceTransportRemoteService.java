package com.teyuntong.goods.web.service.remote.goodsname;

import com.teyuntong.goods.service.client.transport.service.GoodCarPriceTransportRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/5/13 9:39
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "GoodCarPriceTransportRpcService", fallbackFactory = GoodCarPriceTransportRemoteService.GoodCarPriceTransportRemoteFallbackFactory.class)
public interface GoodCarPriceTransportRemoteService extends GoodCarPriceTransportRpcService {

    @Slf4j
    @Component
    class GoodCarPriceTransportRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<GoodCarPriceTransportRpcService> {
        protected GoodCarPriceTransportRemoteFallbackFactory() {
            super(true, GoodCarPriceTransportRpcService.class);
        }
    }

}
