package com.teyuntong.goods.web.service.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Supplier;

/**
 * RPC调用日志记录工具类
 * 用于记录RPC调用的详细信息，包括服务实例的IP地址、端口号等
 * 支持记录实际调用的具体实例信息
 *
 * <AUTHOR>
 * @since 2025/01/09
 */
@Component
@Slf4j
public class RpcCallLogger {

    private final DiscoveryClient discoveryClient;

    // 使用ThreadLocal存储当前线程的实际调用信息
    private static final ThreadLocal<String> ACTUAL_CALL_INFO = new ThreadLocal<>();

    public RpcCallLogger(DiscoveryClient discoveryClient) {
        this.discoveryClient = discoveryClient;
    }

    /**
     * 设置实际调用的实例信息（由Feign拦截器调用）
     */
    public static void setActualCallInfo(String host, int port, String path) {
        ACTUAL_CALL_INFO.set(String.format("实际调用实例 - 主机: %s, 端口: %d, 路径: %s", host, port, path));
    }

    /**
     * 获取并清除实际调用信息
     */
    private static String getAndClearActualCallInfo() {
        try {
            return ACTUAL_CALL_INFO.get();
        } finally {
            ACTUAL_CALL_INFO.remove();
        }
    }

    /**
     * 记录RPC调用信息并执行调用
     *
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @param supplier 实际的RPC调用
     * @param <T> 返回类型
     * @return 调用结果
     */
    public <T> T logAndExecute(String serviceName, String methodName, Supplier<T> supplier) {
        return logAndExecute(serviceName, methodName, null, supplier);
    }

    /**
     * 记录RPC调用信息并执行调用
     *
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @param params 参数信息
     * @param supplier 实际的RPC调用
     * @param <T> 返回类型
     * @return 调用结果
     */
    public <T> T logAndExecute(String serviceName, String methodName, Object params, Supplier<T> supplier) {
        long startTime = System.currentTimeMillis();
        
        // 记录调用开始信息
        log.info("=== RPC调用开始 ===");
        log.info("服务名称: {}", serviceName);
        log.info("方法名称: {}", methodName);
        log.info("调用参数: {}", formatParams(params));
        
        // 记录可用的服务实例信息
        logAvailableInstances(serviceName);
        
        try {
            T result = supplier.get();

            long duration = System.currentTimeMillis() - startTime;

            // 获取实际调用的实例信息
            String actualCallInfo = getAndClearActualCallInfo();

            log.info("=== RPC调用成功 ===");
            log.info("服务名称: {}", serviceName);
            log.info("方法名称: {}", methodName);
            if (actualCallInfo != null) {
                log.info("{}", actualCallInfo);
            }
            log.info("调用耗时: {}ms", duration);
            log.info("返回类型: {}", result != null ? result.getClass().getSimpleName() : "null");
            if (result instanceof List) {
                log.info("返回数量: {}", ((List<?>) result).size());
            }

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;

            // 获取实际调用的实例信息（即使失败也要记录）
            String actualCallInfo = getAndClearActualCallInfo();

            log.error("=== RPC调用失败 ===");
            log.error("服务名称: {}", serviceName);
            log.error("方法名称: {}", methodName);
            if (actualCallInfo != null) {
                log.error("{}", actualCallInfo);
            }
            log.error("调用耗时: {}ms", duration);
            log.error("错误信息: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 记录可用的服务实例信息
     */
    private void logAvailableInstances(String serviceName) {
        try {
            if (discoveryClient != null) {
                List<ServiceInstance> instances = discoveryClient.getInstances(serviceName);
                if (instances != null && !instances.isEmpty()) {
                    log.info("发现可用服务实例数量: {}", instances.size());
                    
                    for (int i = 0; i < instances.size(); i++) {
                        ServiceInstance instance = instances.get(i);
                        log.info("实例[{}] - 主机: {}, 端口: {}, 实例ID: {}, 完整地址: {}", 
                                i + 1, 
                                instance.getHost(), 
                                instance.getPort(), 
                                instance.getInstanceId(), 
                                instance.getUri());
                        
                        // 记录实例的元数据信息
                        if (instance.getMetadata() != null && !instance.getMetadata().isEmpty()) {
                            log.info("实例[{}]元数据: {}", i + 1, instance.getMetadata());
                        }
                    }
                } else {
                    log.warn("未发现可用服务实例 - 服务名称: {}", serviceName);
                }
            } else {
                log.warn("DiscoveryClient未初始化，无法获取服务实例信息");
            }
        } catch (Exception e) {
            log.warn("获取服务实例信息失败 - 服务名称: {}, 错误: {}", serviceName, e.getMessage());
        }
    }

    /**
     * 格式化参数信息
     */
    private String formatParams(Object params) {
        if (params == null) {
            return "null";
        }
        
        String paramStr = params.toString();
        // 如果参数太长，截断显示
        if (paramStr.length() > 200) {
            return params.getClass().getSimpleName() + "(长度=" + paramStr.length() + ", 内容=" + paramStr.substring(0, 100) + "...)";
        }
        
        return paramStr;
    }

    /**
     * 简单的RPC调用记录（无返回值）
     */
    public void logAndExecute(String serviceName, String methodName, Runnable runnable) {
        logAndExecute(serviceName, methodName, null, () -> {
            runnable.run();
            return null;
        });
    }

    /**
     * 带参数的RPC调用记录（无返回值）
     */
    public void logAndExecute(String serviceName, String methodName, Object params, Runnable runnable) {
        logAndExecute(serviceName, methodName, params, () -> {
            runnable.run();
            return null;
        });
    }
}
