package com.teyuntong.goods.web.service.biz.thprice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.TecServiceFeeVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.web.service.biz.thprice.service.ThPriceService;
import com.teyuntong.goods.web.service.remote.thprice.ThPriceRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ThPriceServiceImpl implements ThPriceService {

    @Autowired
    private ThPriceRemoteService thPriceRemoteService;

    @Override
    public CarryPriceVO getThPrice(TransportCarryReq transportCarryReq) {
        log.info("获取优车2.0建议价 请求参数：{}", JSONObject.toJSONString(transportCarryReq));
        return thPriceRemoteService.getThPrice(transportCarryReq);
    }

    @Override
    public TecServiceFeeVO tecServiceFee(PublishDTO publishDTO) {
        log.info("获取技术服务费建议价 请求参数：{}", JSONObject.toJSONString(publishDTO));
        return thPriceRemoteService.tecServiceFee(publishDTO);
    }

}
