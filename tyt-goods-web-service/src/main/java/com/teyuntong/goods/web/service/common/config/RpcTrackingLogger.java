package com.teyuntong.goods.web.service.common.config;

import com.teyuntong.goods.web.service.common.util.RpcCallLogger;
import feign.Logger;
import feign.Request;
import feign.Response;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.URI;

/**
 * RPC跟踪日志记录器
 * 用于捕获Feign实际调用的HTTP请求信息
 *
 * <AUTHOR>
 * @since 2025/01/09
 */
@Slf4j
public class RpcTrackingLogger extends Logger {

    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        if (logLevel.ordinal() >= Level.BASIC.ordinal()) {
            try {
                String url = request.url();
                URI uri = URI.create(url);
                String host = uri.getHost();
                int port = uri.getPort();
                String path = uri.getPath();
                
                // 记录实际调用的实例信息
                RpcCallLogger.setActualCallInfo(host, port, path);
                
                // 记录请求信息
                log.debug("Feign请求 - ConfigKey: {}, URL: {}", configKey, url);
                
            } catch (Exception e) {
                log.warn("解析Feign请求URL失败: {}", request.url(), e);
            }
        }
    }

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime) throws IOException {
        if (logLevel.ordinal() >= Level.BASIC.ordinal()) {
            try {
                String url = response.request().url();
                log.debug("Feign响应 - ConfigKey: {}, URL: {}, Status: {}, Duration: {}ms", 
                        configKey, url, response.status(), elapsedTime);
            } catch (Exception e) {
                log.warn("记录Feign响应信息失败", e);
            }
        }
        
        return super.logAndRebufferResponse(configKey, logLevel, response, elapsedTime);
    }

    @Override
    protected void log(String configKey, String format, Object... args) {
        // 使用slf4j记录日志
        log.debug("Feign - ConfigKey: {}, Message: {}", configKey, String.format(format, args));
    }
}
