package com.teyuntong.goods.web.service.remote.publish;

import com.teyuntong.goods.service.client.publish.service.TransportPublishTypeRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/07/02 15:31
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "transportPublishTypeRemoteService",
        fallbackFactory = TransportPublishTypeRemoteService.TransportPublishTypeRemoteFallbackFactory.class)
public interface TransportPublishTypeRemoteService extends TransportPublishTypeRpcService {

    @Slf4j
    @Component
    class TransportPublishTypeRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TransportPublishTypeRpcService> {
        protected TransportPublishTypeRemoteFallbackFactory() {
            super(true, TransportPublishTypeRpcService.class);
        }
    }

}
