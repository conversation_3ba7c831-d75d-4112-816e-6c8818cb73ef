package com.teyuntong.goods.web.service.biz.publish.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 自动重发显示信息
 *
 * <AUTHOR>
 * @since 2024/12/12 14:42
 */
@Getter
@Setter
public class AutoResendShowDTO {

    /**
     * 是否展示自动重发弹窗：1弹窗0不弹
     */
    private Integer isShow;

    /**
     * 标签：高成交用户专属权益
     */
    private String label;

    /**
     * 文案：今天未成交，明天自动帮您重发，本月剩余XXX次
     */
    private String prompt;

    /**
     * 是否勾选：1勾选0不勾选
     */
    private Integer defaultValue;

    /**
     * 弹窗标题：自动重发权益
     */
    private String popTitle;

    /**
     * 弹窗内容：
     */
    private String popContent;

}
