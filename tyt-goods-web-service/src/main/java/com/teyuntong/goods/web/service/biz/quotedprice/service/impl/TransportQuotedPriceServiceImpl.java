package com.teyuntong.goods.web.service.biz.quotedprice.service.impl;

import com.teyuntong.goods.service.client.quotedprice.dto.QuotedPriceDTO;
import com.teyuntong.goods.service.client.quotedprice.vo.*;
import com.teyuntong.goods.service.client.transport.vo.TransportMainVO;
import com.teyuntong.goods.web.service.biz.quotedprice.mybatis.mapper.TransportQuotedPriceMapper;
import com.teyuntong.goods.web.service.biz.quotedprice.service.TransportQuotedPriceService;
import com.teyuntong.goods.web.service.remote.goodsname.TransportMainRemoteService;
import com.teyuntong.goods.web.service.remote.quotedprice.TransportQuotedPriceRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TransportQuotedPriceServiceImpl implements TransportQuotedPriceService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private TransportQuotedPriceMapper transportQuotedPriceMapper;
    @Autowired
    private TransportQuotedPriceRemoteService transportQuotedPriceRemoteService;
    @Autowired
    private TransportMainRemoteService transportMainRemoteService;

    private static final String TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY = "transportQuotedPriceCount";

    @Override
    public Integer getTransportNoLookOverQuotedPriceCountNum(Long transportUserId) {
        Map<Object, Object> transportQuotedPriceDataMap = stringRedisTemplate.opsForHash().entries(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportUserId);
        Set<Long> srcMsgIdSet = transportQuotedPriceDataMap.keySet().stream()
                .map(object -> Long.valueOf(object.toString()))
                .collect(Collectors.toSet());
        if (srcMsgIdSet.isEmpty()) {
            return 0;
        }
        List<Long> publishIngTransportBySrcMsgId = transportQuotedPriceMapper.getPublishIngTransportBySrcMsgId(new ArrayList<>(srcMsgIdSet));
        List<Integer> countNumList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(publishIngTransportBySrcMsgId)) {
            for (Long srcMsgId : publishIngTransportBySrcMsgId) {
                if (transportQuotedPriceDataMap.containsKey(srcMsgId.toString())) {
                    countNumList.add(Integer.parseInt(String.valueOf(transportQuotedPriceDataMap.get(srcMsgId.toString()))));
                }
            }
        }

        return countNumList.stream().mapToInt(Integer::intValue).sum();
    }

    /**
     * 车获取某个货源自己的报价
     * @param userId
     * @param srcMsgId
     * @return
     */
    @Override
    public TransportQuotedPriceCarVO getCarToTransportQuotedPrice(Long userId, Long srcMsgId) {
        return transportQuotedPriceRemoteService.getCarToTransportQuotedPrice(userId, srcMsgId);
    }

    /**
     * 货获取某个货源的所有报价列表
     * @param srcMsgId
     * @param userId
     * @return
     */
    @Override
    public List<TransportQuotedPriceTransportVO> getTransportQuotedPriceList(Long srcMsgId, Long userId) {
        return transportQuotedPriceRemoteService.getTransportQuotedPriceList(srcMsgId, userId);
    }

    @Override
    public QuotedPriceResultVO transportAgree(QuotedPriceDTO agreeDTO) {
        return transportQuotedPriceRemoteService.transportAgree(agreeDTO);
    }

    @Override
    public QuotedPriceResultVO transportQuotedPrice(QuotedPriceDTO agreeDTO) {
        return transportQuotedPriceRemoteService.transportQuotedPrice(agreeDTO);
    }

    @Override
    public QuotedPriceResultVO carQuotedPrice(QuotedPriceDTO priceDTO) {
        return transportQuotedPriceRemoteService.carQuotedPrice(priceDTO);
    }

    @Override
    public QuotedPriceResultVO carAgree(QuotedPriceDTO priceDTO) {
        return transportQuotedPriceRemoteService.carAgree(priceDTO);
    }

    @Override
    public TransportQuotedPriceLeaveTabVO getTransportQuotedPriceLeaveTab(Long srcMsgId) {
        return transportQuotedPriceRemoteService.getTransportQuotedPriceLeaveTab(srcMsgId);
    }

    @Override
    public TransportQuotedPriceTabDataVO getTransportQuotedPriceTabData(Long transportQuotedPriceId) {
        return transportQuotedPriceRemoteService.getTransportQuotedPriceTabData(transportQuotedPriceId);
    }

    @Override
    public List<AllTransportQuotedPriceVO> getAllPublishingTransportQuotedPriceList() {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        return transportQuotedPriceRemoteService.getAllPublishingTransportQuotedPriceList(loginUser.getUserId());
    }

    @Override
    public Boolean clickTransportQuotedPriceBubble(Long srcMsgId) {
        return transportQuotedPriceRemoteService.clickTransportQuotedPriceBubble(srcMsgId);
    }

    /**
     * 货获取所有发布中货源的所有报价列表
     *
     * @return
     */
    @Override
    public RecordTypeVO transportNewestRecordType() {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        return transportQuotedPriceRemoteService.transportNewestRecordType(loginUser.getUserId());
    }

    /**
     * 车方 被反馈（报价被货方同意）、有回价气泡内容
     *
     * @return
     */
    @Override
    public String getCarHaveNewQuotedPriceOrAgreeQuotedPrice() {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        return transportQuotedPriceRemoteService.getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice(loginUser.getUserId());
    }

    /**
     * 货主是否有货源被车方出价
     *
     * @param userId
     * @return
     */
    @Override
    public Boolean getTransportHaveAnyQuotedPrice(Long userId) {
        return transportQuotedPriceRemoteService.getTransportHaveAnyQuotedPrice(userId);
    }

    /**
     * 车主是否存在货方回价了但车方还没想响应的货
     *
     * @param userId
     * @return
     */
    @Override
    public Boolean getCarHaveNewTransportQuotedPrice(Long userId) {
        return transportQuotedPriceRemoteService.getCarHaveNewTransportQuotedPrice(userId);
    }

    /**
     * 车离开货源详情报价挽留弹窗
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    @Override
    public Boolean getCarLeaveTransportSingleDetailTabData(Long userId, Long srcMsgId) {
        return transportQuotedPriceRemoteService.getCarLeaveTransportSingleDetailTabData(srcMsgId, userId);
    }

    /**
     * 货在报价列表web页面顶部氛围文案
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public String getTransportQuotedPricePageWord(Long srcMsgId) {
        return transportQuotedPriceRemoteService.getTransportQuotedPricePageWord(srcMsgId);
    }

    /**
     * 获取报价货源详情
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    @Override
    public TransportMainVO getTransportVO(Long srcMsgId, Long userId) {
        return transportQuotedPriceRemoteService.getTransportVO(srcMsgId, userId);
    }

    @Override
    public CarShowQuotedPriceBoxVO carShowQuotedPriceBox(Long srcMsgId) {
        return transportQuotedPriceRemoteService.carShowQuotedPriceBox(srcMsgId);
    }

    /**
     * 货源详情报价列表
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    @Override
    public TransportQuotedPriceDataInDetailPageVO getQuotedPriceListInSingleDetailPage(Long srcMsgId, Long userId) {
        return transportQuotedPriceRemoteService.getQuotedPriceListInSingleDetailPage(srcMsgId, userId);
    }

    /**
     * 查询用户是否是货主
     * @param userId
     * @param srcMsgId
     * @return
     */
    @Override
    public boolean checkUserIsTransportOwner(Long userId, Long srcMsgId) {
        return transportMainRemoteService.checkUserIsTransportOwner(userId, srcMsgId);
    }
}
