package com.teyuntong.goods.web.service.remote.thprice;

import com.teyuntong.goods.service.client.transport.service.SameTransportAvgPriceRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "SameTransportAvgPriceRpcService", fallbackFactory = SameTransportAvgPriceRemoteService.SameTransportAvgPriceRemoteServiceFallbackFactory.class)
public interface SameTransportAvgPriceRemoteService extends SameTransportAvgPriceRpcService{

    @Slf4j
    @Component
    class SameTransportAvgPriceRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<SameTransportAvgPriceRpcService> {
        protected SameTransportAvgPriceRemoteServiceFallbackFactory() {
            super(true, SameTransportAvgPriceRpcService.class);
        }
    }

}
