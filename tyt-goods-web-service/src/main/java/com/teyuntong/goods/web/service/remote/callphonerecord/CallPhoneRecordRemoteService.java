package com.teyuntong.goods.web.service.remote.callphonerecord;

import com.teyuntong.goods.service.client.callphonerecord.service.CallPhoneRecordRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "CallPhoneRecordRpcService", fallbackFactory = CallPhoneRecordRemoteService.CallPhoneRecordRemoteFallbackFactory.class)
public interface CallPhoneRecordRemoteService extends CallPhoneRecordRpcService {

    @Slf4j
    @Component
     class CallPhoneRecordRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<CallPhoneRecordRpcService> {
        protected CallPhoneRecordRemoteFallbackFactory() {
            super(true, CallPhoneRecordRpcService.class);
        }
    }
}
