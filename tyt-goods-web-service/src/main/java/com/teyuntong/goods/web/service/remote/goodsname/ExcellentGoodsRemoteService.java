package com.teyuntong.goods.web.service.remote.goodsname;

import com.teyuntong.goods.service.client.excellentgoods.service.ExcellentGoodsRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "ExcellentGoodsRpcService", fallbackFactory = ExcellentGoodsRemoteService.ExcellentGoodsRemoteFallbackFactory.class)
public interface ExcellentGoodsRemoteService extends ExcellentGoodsRpcService {

    @Slf4j
    @Component
    class ExcellentGoodsRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<ExcellentGoodsRpcService> {
        protected ExcellentGoodsRemoteFallbackFactory() {
            super(true, ExcellentGoodsRpcService.class);
        }
    }

}
