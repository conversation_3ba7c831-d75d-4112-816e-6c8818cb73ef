package com.teyuntong.goods.web.service.biz.quotedprice.service;


import com.teyuntong.goods.service.client.quotedprice.dto.QuotedPriceDTO;
import com.teyuntong.goods.service.client.quotedprice.vo.*;
import com.teyuntong.goods.service.client.transport.vo.TransportMainVO;

import java.util.List;

public interface TransportQuotedPriceService {

    Integer getTransportNoLookOverQuotedPriceCountNum(Long transportUserId);

    TransportQuotedPriceCarVO getCarToTransportQuotedPrice(Long userId, Long srcMsgId);

    List<TransportQuotedPriceTransportVO> getTransportQuotedPriceList(Long srcMsgId, Long userId);

    boolean checkUserIsTransportOwner(Long userId, Long srcMsgId);

    TransportQuotedPriceDataInDetailPageVO getQuotedPriceListInSingleDetailPage(Long srcMsgId, Long userId);

    QuotedPriceResultVO transportAgree(QuotedPriceDTO agreeDTO);

    QuotedPriceResultVO transportQuotedPrice(QuotedPriceDTO agreeDTO);

    QuotedPriceResultVO carQuotedPrice(QuotedPriceDTO priceDTO);

    QuotedPriceResultVO carAgree(QuotedPriceDTO priceDTO);

    TransportQuotedPriceLeaveTabVO getTransportQuotedPriceLeaveTab(Long srcMsgId);

    TransportQuotedPriceTabDataVO getTransportQuotedPriceTabData(Long transportQuotedPriceId);

    List<AllTransportQuotedPriceVO> getAllPublishingTransportQuotedPriceList();

    Boolean clickTransportQuotedPriceBubble(Long srcMsgId);

    /**
     * 货获取所有发布中货源的所有报价列表
     *
     * @return
     */
    RecordTypeVO transportNewestRecordType();

    String getCarHaveNewQuotedPriceOrAgreeQuotedPrice();

    Boolean getTransportHaveAnyQuotedPrice(Long userId);

    Boolean getCarHaveNewTransportQuotedPrice(Long userId);

    Boolean getCarLeaveTransportSingleDetailTabData(Long userId, Long srcMsgId);

    String getTransportQuotedPricePageWord(Long srcMsgId);

    TransportMainVO getTransportVO(Long srcMsgId, Long userId);

    CarShowQuotedPriceBoxVO carShowQuotedPriceBox(Long srcMsgId);

}
