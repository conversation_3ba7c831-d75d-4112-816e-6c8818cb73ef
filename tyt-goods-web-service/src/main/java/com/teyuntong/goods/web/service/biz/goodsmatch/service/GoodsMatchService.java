package com.teyuntong.goods.web.service.biz.goodsmatch.service;

import com.teyuntong.goods.service.client.goodsname.dto.CheckScreeningWordDto;
import com.teyuntong.goods.service.client.goodsname.dto.GoodsMatchRpcDto;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsBrandListVo;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsMatchVo;

/**
 * <AUTHOR>
 * @since 2024/4/23 13:32
 */

public interface GoodsMatchService {
    GoodsMatchVo goodsMatch(GoodsMatchRpcDto goodsMatchRpcDto);

    /**
     * 敏感词校验
     *
     * @param checkScreeningWordDto
     */
    void screeningWordCheck(CheckScreeningWordDto checkScreeningWordDto);



    Boolean isEdit(Long srcMsgId, Integer type);

    GoodsBrandListVo goodsBrand(GoodsMatchRpcDto goodsMatchRpcDto);
}
