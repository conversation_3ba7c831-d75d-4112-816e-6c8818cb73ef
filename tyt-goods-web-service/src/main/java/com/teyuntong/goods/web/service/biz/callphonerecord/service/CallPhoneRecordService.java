package com.teyuntong.goods.web.service.biz.callphonerecord.service;

import com.teyuntong.goods.service.client.callphonerecord.vo.GetCarPhoneVo;
import com.teyuntong.goods.service.client.callphonerecord.vo.CallPhoneRecordVo;
import com.teyuntong.goods.service.client.callphonerecord.vo.TransportRecordVo;
import com.teyuntong.goods.web.service.biz.callphonerecord.dto.CallPhoneRecordAddRemarkReq;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/23 13:32
 */

public interface CallPhoneRecordService {

    List<TransportRecordVo> transportList(Long userId);

    Integer getTransportNoLookCallLogCountNum(Long userId);

    void addRemark(CallPhoneRecordAddRemarkReq callPhoneRecordAddRemarkReq);

    /**
     * 货方获取车方联系人电话
     *
     * @param linkUserId
     * @return
     */
    GetCarPhoneVo getPhone(Long linkUserId);

    GetCarPhoneVo getPhoneNoAuth(Long linkUserId);

    /**
     * 意向车源列表
     * @param srcMsgId
     * @param userId
     * @return
     */
    List<CallPhoneRecordVo> contactedList(Long srcMsgId, Long userId);

}
