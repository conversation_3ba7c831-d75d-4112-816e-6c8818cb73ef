package com.teyuntong.goods.web.service.remote.goodsname;

import com.teyuntong.goods.service.client.transport.service.GoodsDetailRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 货源详情页rpc
 *
 * <AUTHOR>
 * @since 2024/12/03 14:27
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "goodsDetailRemoteService",
        fallbackFactory = GoodsDetailRemoteService.GoodsDetailRemoteFallbackFactory.class)
public interface GoodsDetailRemoteService extends GoodsDetailRpcService {
    @Slf4j
    @Component
    class GoodsDetailRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<GoodsDetailRpcService> {
        protected GoodsDetailRemoteFallbackFactory() {
            super(true, GoodsDetailRpcService.class);
        }
    }
}
