package com.teyuntong.goods.web.service.biz.transport.service;

import com.teyuntong.goods.service.client.transport.dto.GoodsPointDTO;
import com.teyuntong.goods.service.client.transport.dto.GoodsPointResultDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportListDTO;
import com.teyuntong.goods.service.client.transport.vo.CancelReasonVO;
import com.teyuntong.goods.service.client.transport.vo.MyTransportVO;


/**
 * 货源服务
 *
 * <AUTHOR>
 * @since 2025-01-06 10:44
 */
public interface TransportService {
    /**
     * 货源列表
     *
     * @param dto
     * @return
     */
    MyTransportVO getMyPublish(TransportListDTO dto);

    /**
     * 发货锚点及发布方式
     *
     * @param dto
     * @return
     */
    GoodsPointResultDTO getGoodsPoint(GoodsPointDTO dto);

    CancelReasonVO cancelReasonList(Long srcMsgId);

    void refresh(Long srcMsgId);
}
