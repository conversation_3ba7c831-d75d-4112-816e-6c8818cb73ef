package com.teyuntong.goods.web.service.biz.publish.service;

import com.teyuntong.goods.service.client.publish.CarouselGoodsDTO;
import com.teyuntong.goods.service.client.publish.dto.*;
import com.teyuntong.goods.service.client.publish.vo.*;
import com.teyuntong.goods.service.client.transport.dto.*;
import com.teyuntong.goods.service.client.publish.vo.DriverAssignPopupVO;
import com.teyuntong.goods.service.client.publish.vo.PriceAssistantVO;
import com.teyuntong.goods.service.client.transport.dto.IntelligentDepositDTO;
import com.teyuntong.goods.service.client.transport.dto.PopUpPriceBoxDTO;
import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.client.transport.dto.ShowRetentionDTO;
import com.teyuntong.goods.service.client.transport.vo.DepositAndRefundTypeVO;
import com.teyuntong.goods.service.client.transport.vo.DirectPublishResultVO;
import com.teyuntong.goods.service.client.transport.vo.PublishGoodsTypeLabelVO;
import com.teyuntong.goods.service.client.transport.vo.TransportPublishVO;
import com.teyuntong.goods.web.service.biz.publish.dto.AutoResendShowDTO;

import java.util.List;

/**
 * 货源发布service
 *
 * <AUTHOR>
 * @since 2024/12/12 14:39
 */
public interface GoodsPublishService {

    /**
     * 发货页是否展示自动重发
     */
    AutoResendShowDTO showAutoResend();

    /**
     * 我的货源页点击直接发布是否弹出出价接口
     */
    PopUpPriceBoxDTO isPopupPriceBox(Long srcMsgId);

    /**
     * 直接发布时是否展示升级为优车发货弹窗
     *
     * @param srcMsgId
     * @return
     */
    UpgradeExcellentDTO showExcellentPricePopup(Long srcMsgId);

    /**
     * 我的货源页点击直接发布是否弹出出价接口
     */
    ShowRetentionDTO showRetention(Long srcMsgId);

    /**
     * 货源发布
     *
     * @param publishDTO
     * @return
     */
    TransportPublishVO saveTransport(PublishDTO publishDTO);

    /**
     * 直接发布
     *
     * @param publishDTO
     * @return
     */
    DirectPublishResultVO directPublish(DirectPublishDTO publishDTO);

    /**
     * 智能订金
     *
     * @param dto
     * @return
     */
    DepositAndRefundTypeVO intelligentDeposit(IntelligentDepositDTO dto);

    /**
     * 保存智能订金用户操作记录
     *
     * @param dto
     */
    void depositRecord(IntelligentDepositDTO dto);

    /**
     * 是否展示回价助手
     *
     * @param dto
     * @return
     */
    PriceAssistantVO showPriceAssistant(PriceAssistantDTO dto);

    /**
     * 保存回价助手信息
     *
     * @param dto
     */
    Boolean savePriceAssistant(PriceAssistantDTO dto);

    /**
     * 清除回价助手红点
     *
     * @return
     */
    Integer clearRedPoint();

    /**
     * 曝光
     *
     * @param publishDTO
     * @return
     */
    DirectPublishResultVO rePublish(DirectPublishDTO publishDTO);

    /**
     * 填价/加价
     *
     * @param publishDTO
     * @return
     */
    DirectPublishResultVO updatePrice(DirectPublishDTO publishDTO);

    /**
     * 转电议/一口价
     *
     * @param publishDTO
     * @return
     */
    DirectPublishResultVO transfer(DirectPublishDTO publishDTO);

    /**
     * 获取预计成交时长
     *
     * @param dto
     * @return
     */
    DealMinutesVO getDealMinutes(DealMinutesDTO dto);

    /**
     * 获取专车发货相关信息
     *
     * @param dto
     * @return
     */
    SpecialCarInfoVO getSpecialCarInfo(SpecialCarInfoDTO dto);

    /**
     * 更新货源信息
     *
     * @param updateGoodsInfoDTO
     * @return
     */
    DirectPublishResultVO updateGoodsInfo(UpdateGoodsInfoDTO updateGoodsInfoDTO);

    /**
     * 我的货源页点击直接发布是否弹出出价接口
     */
    PopUpPriceBoxDTO publishPopupPriceBox(TransportPublishDTO publishDTO);

    /**
     * 货源发布、变更时弹窗埋点记录接口
     */
    void popupTracking(PopupTrackingLogDTO publishDTO);

    /**
     * 获取货源发布类型
     * @param publishGoodsTypeDTO
     * @return
     */
    PublishGoodsTypeResultVO getPublishGoodsType(PublishGoodsTypeDTO publishGoodsTypeDTO);

    /**
     * 获取专车运费
     * @param priceDTO
     * @return
     */
    CalcSpecialGoodsPriceResultDTO calcSpecialGoodsPrice(CalculatePriceDTO priceDTO);

    /**
     * 发货下一步校验
     * @param nextStepDTO
     * @return
     */
    Boolean checkPublishNextStep(CheckNextStepDTO nextStepDTO);

    Boolean checkPrice(CheckNextStepDTO nextStepDTO);

    /**
     * 获取下一步轮播货源数据
     * @param carouselGoodsDTO
     * @return
     */
    List<CarouselGoodsVO> getCarouselGoods(CarouselGoodsDTO carouselGoodsDTO);

    /**
     * 货源指派弹窗
     *
     * @param driverdAssignPopupDTO
     */
    DriverAssignPopupVO driverAssignPopup(DriverAssignPopupDTO driverdAssignPopupDTO);

    /**
     * 获取货源发布类型标签
     * @param publishGoodsTypeLabelDTO
     * @return
     */
    List<PublishGoodsTypeLabelVO> getPublishGoodsTypeLabel(PublishGoodsTypeLabelDTO publishGoodsTypeLabelDTO);
}
