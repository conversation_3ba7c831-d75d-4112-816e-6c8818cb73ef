package com.teyuntong.goods.web.service.common.config;

import feign.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign配置类，用于记录RPC调用的详细信息
 *
 * <AUTHOR>
 * @since 2025/01/09
 */
@Configuration
public class FeignConfig {

    /**
     * 配置Feign日志级别
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }
}
