package com.teyuntong.goods.web.service.common.config;

import feign.Client;
import feign.Logger;
import feign.Request;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.io.IOException;
import java.net.URI;

/**
 * Feign配置类，用于记录RPC调用的详细信息
 *
 * <AUTHOR>
 * @since 2025/01/09
 */
@Configuration
@Slf4j
public class FeignConfig {

    /**
     * 配置Feign日志级别
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    /**
     * 自定义Feign客户端，用于记录RPC调用的实例信息
     */
    @Bean
    @Primary
    public Client feignClient(Client delegate) {
        return new RpcLoggingFeignClient(delegate);
    }

    /**
     * RPC日志记录的Feign客户端包装器
     */
    @Slf4j
    static class RpcLoggingFeignClient implements Client {
        private final Client delegate;

        public RpcLoggingFeignClient(Client delegate) {
            this.delegate = delegate;
        }

        @Override
        public Response execute(Request request, Request.Options options) throws IOException {
            long startTime = System.currentTimeMillis();
            
            // 记录请求开始信息
            String url = request.url();
            String method = request.httpMethod().name();
            
            try {
                // 解析URL获取主机和端口信息
                URI uri = URI.create(url);
                String host = uri.getHost();
                int port = uri.getPort();
                String path = uri.getPath();
                String query = uri.getQuery();
                
                log.info("RPC调用开始 - Method: {}, Host: {}, Port: {}, Path: {}, Query: {}", 
                        method, host, port, path, query);
                
                // 执行实际的请求
                Response response = delegate.execute(request, options);
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                
                // 记录响应信息
                log.info("RPC调用完成 - Method: {}, Host: {}, Port: {}, Path: {}, Status: {}, Duration: {}ms", 
                        method, host, port, path, response.status(), duration);
                
                return response;
                
            } catch (Exception e) {
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                
                // 记录异常信息
                log.error("RPC调用异常 - Method: {}, URL: {}, Duration: {}ms, Error: {}", 
                        method, url, duration, e.getMessage(), e);
                
                throw e;
            }
        }
    }
}
