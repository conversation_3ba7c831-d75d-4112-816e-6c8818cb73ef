package com.teyuntong.goods.web.service.remote.quotedprice;

import com.teyuntong.goods.service.client.quotedprice.service.TransportQuotedPriceRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 货源出价服务
 *
 * <AUTHOR>
 * @since 2024-11-05 17:31
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "TransportQuotedPriceRpcService", fallbackFactory = TransportQuotedPriceRemoteService.TransportQuotedPriceRemoteServiceFallbackFactory.class)
public interface TransportQuotedPriceRemoteService extends TransportQuotedPriceRpcService {
    @Component
    @Slf4j
    class TransportQuotedPriceRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TransportQuotedPriceRpcService> {
        protected TransportQuotedPriceRemoteServiceFallbackFactory() {
            super(true, TransportQuotedPriceRpcService.class);
        }
    }
}
