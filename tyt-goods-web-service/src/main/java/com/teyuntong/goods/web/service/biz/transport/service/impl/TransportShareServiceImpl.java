package com.teyuntong.goods.web.service.biz.transport.service.impl;

import com.teyuntong.goods.service.client.transport.dto.TransportShareDTO;
import com.teyuntong.goods.web.service.biz.transport.service.TransportShareService;
import com.teyuntong.goods.web.service.remote.share.TransportShareRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 货源分享
 *
 * <AUTHOR>
 * @since 2025-06-19 15:32
 */
@Service
@Slf4j
public class TransportShareServiceImpl implements TransportShareService {
    @Resource
    private TransportShareRemoteService transportShareRemoteService;

    /**
     * 保存货源分享记录
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean add(TransportShareDTO dto) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        dto.setUserId(loginUser.getUserId());
        return transportShareRemoteService.saveShareRecord(dto);
    }
}
