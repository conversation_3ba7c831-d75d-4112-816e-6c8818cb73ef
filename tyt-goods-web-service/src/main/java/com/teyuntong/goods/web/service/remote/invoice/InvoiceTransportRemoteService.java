package com.teyuntong.goods.web.service.remote.invoice;

import com.teyuntong.goods.service.client.invoice.service.InvoiceTransportRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/5/13 9:39
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "InvoiceTransportRpcService", fallbackFactory = InvoiceTransportRemoteService.InvoiceTransportRemoteFallbackFactory.class)
public interface InvoiceTransportRemoteService extends InvoiceTransportRpcService {

    @Slf4j
    @Component
    class InvoiceTransportRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<InvoiceTransportRpcService> {
        protected InvoiceTransportRemoteFallbackFactory() {
            super(true, InvoiceTransportRpcService.class);
        }
    }

}
