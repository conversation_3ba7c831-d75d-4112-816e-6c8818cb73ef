package com.teyuntong.goods.web.service.remote.navigation;

import com.teyuntong.goods.service.client.navigation.service.NavigationRpcService;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/5/13 9:39
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "navigationRemoteService", fallbackFactory = NavigationRemoteService.TransportMainRemoteFallbackFactory.class)
public interface NavigationRemoteService extends NavigationRpcService {

    @Slf4j
    @Component
    class TransportMainRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TransportMainRpcService> {
        protected TransportMainRemoteFallbackFactory() {
            super(true, TransportMainRpcService.class);
        }
    }

}
