package com.teyuntong.goods.web.service.remote.publish;

import com.teyuntong.goods.service.client.transport.service.IntelligentDepositRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 智能订金
 *
 * <AUTHOR>
 * @since 2024/12/03 14:27
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "IntelligentDepositRemoteService",
        fallbackFactory = IntelligentDepositRemoteService.IntelligentDepositRemoteFallbackFactory.class)
public interface IntelligentDepositRemoteService extends IntelligentDepositRpcService {
    @Slf4j
    @Component
    class IntelligentDepositRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<IntelligentDepositRpcService> {
        protected IntelligentDepositRemoteFallbackFactory() {
            super(true, IntelligentDepositRpcService.class);
        }
    }
}
