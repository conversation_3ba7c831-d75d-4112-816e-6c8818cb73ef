package com.teyuntong.goods.web.service.common.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 公共配置
 *
 * <AUTHOR>
 * @since 2024/12/12 16:55
 */
@Component
@ConfigurationProperties(prefix = "tyt.prompt")
@Getter
@Setter
public class PromptProperties {
    /**
     * 自动重发弹窗内容
     */
    private String autoResendPopContent;
}
