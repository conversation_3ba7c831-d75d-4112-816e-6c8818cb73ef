package com.teyuntong.goods.web.service.remote.publish;

import com.teyuntong.goods.service.client.publish.service.TransportPublishRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 货源发布接口service
 *
 * <AUTHOR>
 * @since 2024/12/03 14:27
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "transportPublishRemoteService",
        fallbackFactory = TransportPublishRemoteService.TransportPublishRemoteFallbackFactory.class)
public interface TransportPublishRemoteService extends TransportPublishRpcService {
    @Slf4j
    @Component
    class TransportPublishRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TransportPublishRpcService> {
        protected TransportPublishRemoteFallbackFactory() {
            super(true, TransportPublishRpcService.class);
        }
    }
}
