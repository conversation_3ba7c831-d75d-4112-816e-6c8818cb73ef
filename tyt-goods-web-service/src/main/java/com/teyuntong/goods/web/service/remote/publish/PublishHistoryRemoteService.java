package com.teyuntong.goods.web.service.remote.publish;

import com.teyuntong.goods.service.client.transport.service.PublishHistoryRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 历史发货货源信息
 *
 * <AUTHOR>
 * @since 2025/12/03 14:27
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "publishHistoryRemoteService", fallbackFactory = PublishHistoryRemoteService.PublishHistoryRemoteFallbackFactory.class)
public interface PublishHistoryRemoteService extends PublishHistoryRpcService {
    @Slf4j
    @Component
    class PublishHistoryRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<PublishHistoryRpcService> {
        protected PublishHistoryRemoteFallbackFactory() {
            super(true, PublishHistoryRpcService.class);
        }
    }
}
