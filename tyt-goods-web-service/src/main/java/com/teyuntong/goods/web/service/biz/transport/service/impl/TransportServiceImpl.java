package com.teyuntong.goods.web.service.biz.transport.service.impl;

import com.teyuntong.goods.service.client.transport.dto.GoodsPointDTO;
import com.teyuntong.goods.service.client.transport.dto.GoodsPointResultDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportListDTO;
import com.teyuntong.goods.service.client.transport.vo.CancelReasonVO;
import com.teyuntong.goods.service.client.transport.vo.MyTransportVO;
import com.teyuntong.goods.web.service.biz.transport.service.TransportService;
import com.teyuntong.goods.web.service.common.error.TytGoodsWebErrorCode;
import com.teyuntong.goods.web.service.remote.goodsname.TransportCancelReasonRemoteService;
import com.teyuntong.goods.web.service.remote.goodsname.TransportMainRemoteService;
import com.teyuntong.goods.web.service.remote.goodsname.TransportRefreshRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.error.ErrorCodeBase;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * 货源服务
 *
 * <AUTHOR>
 * @since 2025-01-06 10:45
 */
@Service
public class TransportServiceImpl implements TransportService {
    @Autowired
    private TransportMainRemoteService transportMainRemoteService;
    @Autowired
    private TransportCancelReasonRemoteService transportCancelReasonRemoteService;
    @Autowired
    private TransportRefreshRemoteService transportRefreshRemoteService;

    @Override
    public MyTransportVO getMyPublish(TransportListDTO dto) {
        return transportMainRemoteService.getMyPublish(dto);
    }

    @Override
    public GoodsPointResultDTO getGoodsPoint(GoodsPointDTO dto) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        dto.setUserId(loginUser.getUserId());

        return transportMainRemoteService.getGoodsPoint(dto);
    }

    @Override
    public CancelReasonVO cancelReasonList(Long srcMsgId) {
        return transportCancelReasonRemoteService.getCancelReasonList(srcMsgId);
    }

    @Override
    public void refresh(Long srcMsgId) {
        transportRefreshRemoteService.refreshTransport(srcMsgId, "cancelPopupWindowGiveRefresh");
    }


}
