package com.teyuntong.goods.web.service.biz.goodsmatch.service.impl;

import com.teyuntong.goods.service.client.goodsname.dto.CheckScreeningWordDto;
import com.teyuntong.goods.service.client.goodsname.dto.GoodsMatchRpcDto;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsBrandListVo;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsMatchVo;
import com.teyuntong.goods.web.service.biz.goodsmatch.service.GoodsMatchService;
import com.teyuntong.goods.web.service.remote.goodsname.GoodsMatchRemoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/4/23 13:34
 */
@Service
@RequiredArgsConstructor
public class GoodsMatchServiceImpl implements GoodsMatchService {

    private final GoodsMatchRemoteService goodsMatchRemoteService;

    @Override
    public GoodsMatchVo goodsMatch(GoodsMatchRpcDto goodsMatchRpcDto) {
        return goodsMatchRemoteService.searchGoodsMatch(goodsMatchRpcDto);
    }

    @Override
    public void screeningWordCheck(CheckScreeningWordDto checkScreeningWordDto) {
        goodsMatchRemoteService.screeningWordCheck(checkScreeningWordDto);
    }

    @Override
    public Boolean isEdit(Long srcMsgId, Integer type) {
        return goodsMatchRemoteService.isEdit(srcMsgId, type);
    }

    @Override
    public GoodsBrandListVo goodsBrand(GoodsMatchRpcDto goodsMatchRpcDto) {
        return goodsMatchRemoteService.searchGoodsBrand(goodsMatchRpcDto);
    }
}
