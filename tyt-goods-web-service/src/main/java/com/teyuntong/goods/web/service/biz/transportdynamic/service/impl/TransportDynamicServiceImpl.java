package com.teyuntong.goods.web.service.biz.transportdynamic.service.impl;

import com.teyuntong.goods.service.client.transport.vo.TransportDynamicVO;
import com.teyuntong.goods.web.service.biz.transportdynamic.service.TransportDynamicService;
import com.teyuntong.goods.web.service.remote.goodsname.TransportMainRemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TransportDynamicServiceImpl implements TransportDynamicService {

    @Autowired
    private TransportMainRemoteService transportMainRemoteService;

    @Override
    public TransportDynamicVO getTransportDynamic(Long userId) {
        return transportMainRemoteService.getTransportDynamic(userId);
    }

}
