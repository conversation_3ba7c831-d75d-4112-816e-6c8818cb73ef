package com.teyuntong.goods.web.service.remote.publish;

import com.teyuntong.goods.service.client.publish.service.PriceAssistantRpcService;
import com.teyuntong.goods.service.client.transport.service.IntelligentDepositRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 回价助手
 *
 * <AUTHOR>
 * @since 2025/05/30 14:27
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "PriceAssistantRpcService",
        fallbackFactory = PriceAssistantRemoteService.PriceAssistantRemoteFallbackFactory.class)
public interface PriceAssistantRemoteService extends PriceAssistantRpcService {
    @Slf4j
    @Component
    class PriceAssistantRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<PriceAssistantRpcService> {
        protected PriceAssistantRemoteFallbackFactory() {
            super(true, PriceAssistantRpcService.class);
        }
    }
}
