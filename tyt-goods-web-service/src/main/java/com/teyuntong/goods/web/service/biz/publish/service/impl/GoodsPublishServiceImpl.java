package com.teyuntong.goods.web.service.biz.publish.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.log.Log;
import com.teyuntong.goods.service.client.publish.CarouselGoodsDTO;
import com.teyuntong.goods.service.client.publish.dto.*;
import com.teyuntong.goods.service.client.publish.vo.*;
import com.teyuntong.goods.service.client.publish.vo.DriverAssignPopupVO;
import com.teyuntong.goods.service.client.publish.vo.PriceAssistantVO;
import com.teyuntong.goods.service.client.transport.dto.IntelligentDepositDTO;
import com.teyuntong.goods.service.client.transport.dto.PopUpPriceBoxDTO;
import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.client.transport.dto.ShowRetentionDTO;
import com.teyuntong.goods.service.client.transport.dto.*;
import com.teyuntong.goods.service.client.transport.service.IntelligentDepositRpcService;
import com.teyuntong.goods.service.client.transport.vo.DepositAndRefundTypeVO;
import com.teyuntong.goods.service.client.transport.vo.DirectPublishResultVO;
import com.teyuntong.goods.service.client.transport.vo.PublishGoodsTypeLabelVO;
import com.teyuntong.goods.service.client.transport.vo.TransportPublishVO;
import com.teyuntong.goods.web.service.biz.publish.dto.AutoResendShowDTO;
import com.teyuntong.goods.web.service.biz.publish.service.GoodsPublishService;
import com.teyuntong.goods.web.service.common.properties.PromptProperties;
import com.teyuntong.goods.web.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.web.service.remote.publish.*;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 货源发布service
 *
 * <AUTHOR>
 * @since 2024/12/12 14:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsPublishServiceImpl implements GoodsPublishService {

    private final TransportPublishRemoteService publishRemoteService;
    private final TransportDirectPublishRemoteService directPublishRemoteService;
    private final IntelligentDepositRemoteService intelligentDepositRemoteService;
    private final PriceAssistantRemoteService priceAssistantRemoteService;
    private final PromptProperties promptProperties;
    private final ConfigRemoteService configRemoteService;
    private final TransportPublishTypeRemoteService transportPublishTypeRemoteService;
    private final TransportPublishRemoteService transportPublishRemoteService;


    @Override
    public TransportPublishVO saveTransport(PublishDTO publishDTO) {
        return publishRemoteService.transportPublish(publishDTO);
    }

    /**
     * 直接发布
     *
     * @param publishDTO
     * @return
     */
    @Override
    public DirectPublishResultVO directPublish(DirectPublishDTO publishDTO) {
        return directPublishRemoteService.directPublish(publishDTO);
    }

    /**
     * 智能订金
     *
     * @param dto
     * @return
     */
    @Override
    public DepositAndRefundTypeVO intelligentDeposit(IntelligentDepositDTO dto) {
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();
        dto.setUserId(user.getUserId());
        return intelligentDepositRemoteService.intelligentDeposit(dto);
    }

    /**
     * 保存智能订金用户操作记录
     *
     * @param dto
     */
    @Override
    public void depositRecord(IntelligentDepositDTO dto) {
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();
        dto.setUserId(user.getUserId());
        intelligentDepositRemoteService.depositRecord(dto);
    }

    /**
     * 是否展示回价助手
     *
     * @param dto
     * @return
     */
    @Override
    public PriceAssistantVO showPriceAssistant(PriceAssistantDTO dto) {
        return priceAssistantRemoteService.showPriceAssistant(dto);
    }

    /**
     * 保存回价助手信息
     *
     * @param dto
     */
    @Override
    public Boolean savePriceAssistant(PriceAssistantDTO dto) {
        return priceAssistantRemoteService.savePriceAssistant(dto);
    }

    /**
     * 清除回价助手红点
     *
     * @return
     */
    @Override
    public Integer clearRedPoint() {
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();
        return priceAssistantRemoteService.clearRedPoint(user.getUserId());
    }

    /**
     * 曝光
     *
     * @param publishDTO
     * @return
     */
    @Override
    public DirectPublishResultVO rePublish(DirectPublishDTO publishDTO) {
        return directPublishRemoteService.rePublish(publishDTO);
    }

    /**
     * 填价/加价
     *
     * @param publishDTO
     * @return
     */
    @Override
    public DirectPublishResultVO updatePrice(DirectPublishDTO publishDTO) {
        return directPublishRemoteService.updatePrice(publishDTO);
    }

    /**
     * 转电议/一口价
     *
     * @param publishDTO
     * @return
     */
    @Override
    public DirectPublishResultVO transfer(DirectPublishDTO publishDTO) {
        return directPublishRemoteService.transfer(publishDTO);
    }

    @Override
    public DealMinutesVO getDealMinutes(DealMinutesDTO dto) {
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();
        dto.setUserId(user.getUserId());
        return transportPublishRemoteService.getDealMinutes(dto);
    }

    @Override
    public SpecialCarInfoVO getSpecialCarInfo(SpecialCarInfoDTO dto) {
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();
        dto.setUserId(user.getUserId());
        return transportPublishRemoteService.getSpecialCarInfo(dto);
    }

    /**
     * 更新货源信息
     *
     * @param updateGoodsInfoDTO
     * @return
     */
    @Override
    public DirectPublishResultVO updateGoodsInfo(UpdateGoodsInfoDTO updateGoodsInfoDTO) {
        return directPublishRemoteService.updateGoodsInfo(updateGoodsInfoDTO);
    }

    /**
     * 发货页是否展示自动重发
     */
    @Override
    public AutoResendShowDTO showAutoResend() {
        // 自动重发时间，如果是18点，则18点之后发货都展示自动重发。如果配置>24，则都不会展示。如果<=0，则始终显示
        Integer hour = configRemoteService.getIntValue("goods:publish:autoResend:hour", 18);
        if (DateUtil.hour(new Date(), true) >= hour) {
            return returnAutoResendShowDTO();
        }
        // 查询自动重发剩余次数，>0展示自动重发
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        if (publishRemoteService.getAutoResendRemainTimes(userId) > 0) {
            return returnAutoResendShowDTO();
        }

        return new AutoResendShowDTO();
    }

    /**
     * 返回自动重发弹窗信息
     */
    private AutoResendShowDTO returnAutoResendShowDTO() {
        AutoResendShowDTO showDTO = new AutoResendShowDTO();

        showDTO.setIsShow(1);
        showDTO.setLabel("高成交用户专属权益");
        showDTO.setPrompt("今天未成交，明天自动帮您重发");
        showDTO.setDefaultValue(1);
        showDTO.setPopTitle("自动重发权益");
        showDTO.setPopContent(promptProperties.getAutoResendPopContent());
        return showDTO;
    }

    /**
     * 我的货源页点击直接发布是否弹出出价接口
     */
    @Override
    public PopUpPriceBoxDTO isPopupPriceBox(Long srcMsgId) {
        return publishRemoteService.isPopupPriceBox(srcMsgId);
    }

    /**
     * 直接发布时是否展示升级为优车发货弹窗
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public UpgradeExcellentDTO showExcellentPricePopup(Long srcMsgId) {
        return publishRemoteService.showExcellentPricePopup(srcMsgId);
    }

    /**
     * 我的货源页点击直接发布是否弹出出价接口
     *
     * @param srcMsgId
     */
    @Override
    public ShowRetentionDTO showRetention(Long srcMsgId) {
        return publishRemoteService.showRetention(srcMsgId);
    }

    /**
     * 我的货源页点击直接发布是否弹出出价接口
     */
    @Override
    public PopUpPriceBoxDTO publishPopupPriceBox(TransportPublishDTO publishDTO) {
        return publishRemoteService.publishPopupPriceBox(publishDTO);
    }

    /**
     * 货源发布、变更时弹窗埋点记录接口
     *
     * @param publishDTO
     */
    @Override
    public void popupTracking(PopupTrackingLogDTO publishDTO) {
        publishRemoteService.popupTracking(publishDTO);
    }

    @Override
    public PublishGoodsTypeResultVO getPublishGoodsType(PublishGoodsTypeDTO publishGoodsTypeDTO) {
        return transportPublishTypeRemoteService.getPublishGoodsType(publishGoodsTypeDTO);
    }

    @Override
    public CalcSpecialGoodsPriceResultDTO calcSpecialGoodsPrice(CalculatePriceDTO priceDTO) {
        return publishRemoteService.calcSpecialGoodsPrice(priceDTO);
    }

    @Override
    public Boolean checkPublishNextStep(CheckNextStepDTO nextStepDTO) {
        return transportPublishTypeRemoteService.checkPublishNextStep(nextStepDTO);
    }

    @Override
    public Boolean checkPrice(CheckNextStepDTO nextStepDTO) {
        return transportPublishTypeRemoteService.checkPrice(nextStepDTO);
    }

    @Override
    public List<CarouselGoodsVO> getCarouselGoods(CarouselGoodsDTO carouselGoodsDTO) {
        return transportPublishTypeRemoteService.getCarouselGoods(carouselGoodsDTO);
    }

    /**
     * 货源指派弹窗
     *
     * @param driverAssignPopupDTO
     */
    @Override
    public DriverAssignPopupVO driverAssignPopup(DriverAssignPopupDTO driverAssignPopupDTO) {
        return publishRemoteService.getDriverAssignPopup(driverAssignPopupDTO);
    }

    @Override
    public List<PublishGoodsTypeLabelVO> getPublishGoodsTypeLabel(PublishGoodsTypeLabelDTO publishGoodsTypeLabelDTO) {
        return transportPublishTypeRemoteService.getPublishGoodsTypeLabel(publishGoodsTypeLabelDTO);
    }
}
