package com.teyuntong.goods.web.service.biz.callphonerecord.service.impl;

import com.teyuntong.goods.service.client.callphonerecord.vo.GetCarPhoneVo;
import com.teyuntong.goods.service.client.callphonerecord.vo.CallPhoneRecordVo;
import com.teyuntong.goods.service.client.callphonerecord.vo.TransportRecordVo;
import com.teyuntong.goods.web.service.biz.callphonerecord.dto.CallPhoneRecordAddRemarkReq;
import com.teyuntong.goods.web.service.biz.callphonerecord.service.CallPhoneRecordService;
import com.teyuntong.goods.web.service.biz.quotedprice.mybatis.mapper.TransportQuotedPriceMapper;
import com.teyuntong.goods.web.service.remote.callphonerecord.CallPhoneRecordRemoteService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CallPhoneRecordServiceImpl implements CallPhoneRecordService {

    private final CallPhoneRecordRemoteService callPhoneRecordRemoteService;

    private final StringRedisTemplate stringRedisTemplate;

    private final TransportQuotedPriceMapper transportQuotedPriceMapper;

    private static final String TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY = "transportCallPhoneRecoredCount";

    @Override
    public List<TransportRecordVo> transportList(Long userId) {
        return callPhoneRecordRemoteService.transportList(userId);
    }

    @Override
    public Integer getTransportNoLookCallLogCountNum(Long transportUserId) {
        Map<Object, Object> transportNoLookCallLogDataMap = stringRedisTemplate.opsForHash().entries(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportUserId);
        Set<Long> srcMsgIdSet = transportNoLookCallLogDataMap.keySet().stream()
                .map(object -> Long.valueOf(object.toString()))
                .collect(Collectors.toSet());
        if (srcMsgIdSet.isEmpty()) {
            return 0;
        }
        List<Long> publishIngTransportBySrcMsgId = transportQuotedPriceMapper.getPublishIngTransportBySrcMsgId(new ArrayList<>(srcMsgIdSet));
        int countNumList = 0;
        if (CollectionUtils.isNotEmpty(publishIngTransportBySrcMsgId)) {
            for (Long srcMsgId : publishIngTransportBySrcMsgId) {
                if (transportNoLookCallLogDataMap.containsKey(srcMsgId.toString())) {
                    String carUserIdArray = String.valueOf(transportNoLookCallLogDataMap.get(srcMsgId.toString()));
                    if (StringUtils.isNotBlank(carUserIdArray)) {
                        Set<String> uniqueUserIds = Arrays.stream(carUserIdArray.split(","))
                                .collect(Collectors.toSet());
                        if (!uniqueUserIds.isEmpty()) {
                            countNumList += uniqueUserIds.size();
                        }
                    }
                }
            }
        }

        return countNumList;
    }

    @Override
    public void addRemark(CallPhoneRecordAddRemarkReq callPhoneRecordAddRemarkReq) {
        transportQuotedPriceMapper.callPhoneRecordDeleteRemark(callPhoneRecordAddRemarkReq.getSrcMsgId(), callPhoneRecordAddRemarkReq.getCarUserId());
        transportQuotedPriceMapper.callPhoneRecordAddRemark(callPhoneRecordAddRemarkReq.getSrcMsgId(), callPhoneRecordAddRemarkReq.getCarUserId(), callPhoneRecordAddRemarkReq.getRemark());
    }

    @Override
    public GetCarPhoneVo getPhone(Long linkUserId) {
        return callPhoneRecordRemoteService.getPhone(linkUserId);
    }

    @Override
    public GetCarPhoneVo getPhoneNoAuth(Long linkUserId) {
        return callPhoneRecordRemoteService.getPhoneNoAuth(linkUserId);
    }

    @Override
    public List<CallPhoneRecordVo> contactedList(Long srcMsgId, Long userId) {
        return callPhoneRecordRemoteService.contactedList(srcMsgId, userId);
    }

}
