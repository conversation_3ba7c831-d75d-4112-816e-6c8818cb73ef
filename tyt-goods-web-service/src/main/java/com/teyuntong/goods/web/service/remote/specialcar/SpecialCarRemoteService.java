package com.teyuntong.goods.web.service.remote.specialcar;

import com.teyuntong.goods.service.client.specialcar.service.SpecialCarRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/06/30 17:39
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "SpecialCarRpcService", fallbackFactory = SpecialCarRemoteService.SpecialCarRemoteServiceFallbackFactory.class)
public interface SpecialCarRemoteService extends SpecialCarRpcService {
    @Component
    @Slf4j
    class SpecialCarRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<SpecialCarRemoteService> {
        protected SpecialCarRemoteServiceFallbackFactory() {
            super(true, SpecialCarRemoteService.class);
        }
    }
}
