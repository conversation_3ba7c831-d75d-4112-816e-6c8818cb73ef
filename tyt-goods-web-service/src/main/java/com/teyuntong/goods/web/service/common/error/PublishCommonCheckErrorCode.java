package com.teyuntong.goods.web.service.common.error;

import com.teyuntong.infra.common.definition.error.ErrorCodeBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特运通错误码ErrorCode
 * <p>
 * （1）总共8位，1 2 位保留
 * （2）3 4 位代表具体的子模块
 * （3）5 6 7 8 位表示具体的业务
 * <p>
 * TODO 修改文件名
 */
@AllArgsConstructor
@Getter
public enum PublishCommonCheckErrorCode implements ErrorCodeBase {
    /**
     * TODO 修改业务错误码第3、4位 详见{@link ErrorCodeBase}
     */
    DISTANCE_ERROR("00040002", "正在获取运距，请稍后重试", "warn", false),
    ;

    private final String code;
    private final String msg;
    private final String logLevel;
    private final boolean success;
}
