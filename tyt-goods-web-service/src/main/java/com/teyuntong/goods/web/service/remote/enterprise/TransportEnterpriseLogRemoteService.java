package com.teyuntong.goods.web.service.remote.enterprise;

import com.teyuntong.goods.service.client.transport.service.TransportEnterpriseLogRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/5/13 9:39
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "TransportEnterpriseLogRpcService", fallbackFactory = TransportEnterpriseLogRemoteService.TransportEnterpriseLogRemoteFallbackFactory.class)
public interface TransportEnterpriseLogRemoteService extends TransportEnterpriseLogRpcService {

    @Slf4j
    @Component
    class TransportEnterpriseLogRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TransportEnterpriseLogRpcService> {
        protected TransportEnterpriseLogRemoteFallbackFactory() {
            super(true, TransportEnterpriseLogRpcService.class);
        }
    }

}
