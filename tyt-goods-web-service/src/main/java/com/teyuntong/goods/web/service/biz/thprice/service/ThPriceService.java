package com.teyuntong.goods.web.service.biz.thprice.service;

import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.TecServiceFeeVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;

public interface ThPriceService {

    CarryPriceVO getThPrice(TransportCarryReq transportCarryReq);

    TecServiceFeeVO tecServiceFee(PublishDTO publishDTO);

}
