package com.teyuntong.goods.web.service.remote.publish;

import com.teyuntong.goods.service.client.publish.service.TransportDirectPublishRpcService;
import com.teyuntong.goods.service.client.publish.service.TransportPublishRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 货源直接发布接口service
 *
 * <AUTHOR>
 * @since 2024/12/03 14:27
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "transportDirectPublishRemoteService",
        fallbackFactory = TransportDirectPublishRemoteService.TransportDirectPublishRemoteFallbackFactory.class)
public interface TransportDirectPublishRemoteService extends TransportDirectPublishRpcService {
    @Slf4j
    @Component
    class TransportDirectPublishRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TransportPublishRpcService> {
        protected TransportDirectPublishRemoteFallbackFactory() {
            super(true, TransportPublishRpcService.class);
        }
    }
}
