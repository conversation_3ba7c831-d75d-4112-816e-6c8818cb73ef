package com.teyuntong.goods.web.service.remote.goodsname;

import com.teyuntong.goods.service.client.transport.service.TransportCancelReasonRpcService;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/2/16 9:39
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "TransportCancelReasonRpcService", fallbackFactory = TransportCancelReasonRemoteService.TransportCancelReasonRemoteFallbackFactory.class)
public interface TransportCancelReasonRemoteService extends TransportCancelReasonRpcService {

    @Slf4j
    @Component
    class TransportCancelReasonRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TransportCancelReasonRpcService> {
        protected TransportCancelReasonRemoteFallbackFactory() {
            super(true, TransportCancelReasonRpcService.class);
        }
    }

}
