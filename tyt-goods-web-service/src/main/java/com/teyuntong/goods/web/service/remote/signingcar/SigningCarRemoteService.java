package com.teyuntong.goods.web.service.remote.signingcar;

import com.teyuntong.goods.service.client.specialcar.service.SigningCarRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/06/30 17:39
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "SigningCarRpcService", fallbackFactory = SigningCarRemoteService.SigningCarRemoteServiceFallbackFactory.class)
public interface SigningCarRemoteService extends SigningCarRpcService {
    @Component
    @Slf4j
    class SigningCarRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<SigningCarRemoteService> {
        protected SigningCarRemoteServiceFallbackFactory() {
            super(true, SigningCarRemoteService.class);
        }
    }
}
