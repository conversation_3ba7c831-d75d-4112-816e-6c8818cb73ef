<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.web.service.biz.quotedprice.mybatis.mapper.TransportQuotedPriceMapper">

  <select id="getPublishIngTransportBySrcMsgId" resultType="java.lang.Long">
    select src_msg_id
    from tyt_transport_main where status = 1 and src_msg_id in
    <foreach collection="srcMsgIdList" item="item" index="index" open="(" separator="," close=")">
        #{item}
    </foreach>
  </select>

  <delete id="callPhoneRecordDeleteRemark">
    delete from call_phone_record_remark where src_msg_id = #{srcMsgId} and car_user_id = #{carUserId}
  </delete>

  <insert id="callPhoneRecordAddRemark">
    insert into call_phone_record_remark (src_msg_id, car_user_id, remark, create_time, modify_time) VALUE (#{srcMsgId}, #{carUserId}, #{remark}, now(), now())
  </insert>

</mapper>