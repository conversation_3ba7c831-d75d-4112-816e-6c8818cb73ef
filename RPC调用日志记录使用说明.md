# RPC调用日志记录使用说明

## 概述

本方案通过 `RpcCallLogger` 工具类来记录RPC调用的详细信息，包括：
- 调用的服务实例IP地址和端口号
- 请求路径和参数
- 调用耗时
- 返回结果信息

## 核心组件

### 1. RpcCallLogger 工具类
位置：`tyt-goods-web-service/src/main/java/com/teyuntong/goods/web/service/common/util/RpcCallLogger.java`

主要功能：
- 记录RPC调用前的服务实例信息
- 记录调用耗时
- 记录调用结果或异常信息

### 2. FeignConfig 配置类
位置：`tyt-goods-web-service/src/main/java/com/teyuntong/goods/web/service/common/config/FeignConfig.java`

功能：启用Feign的FULL级别日志记录

### 3. 日志配置
位置：`tyt-goods-web-starter/src/main/resources/bootstrap.yaml`

配置了Feign客户端的debug级别日志

## 使用方法

### 1. 在Controller中注入RpcCallLogger

```java
@RequiredArgsConstructor
@RestController
public class YourController {
    
    private final YourRemoteService yourRemoteService;
    private final RpcCallLogger rpcCallLogger;
    
    // ... 方法实现
}
```

### 2. 使用RpcCallLogger包装RPC调用

#### 有返回值的调用：
```java
@GetMapping("/example")
public WebResult<List<SomeVO>> example() {
    LoginUserDTO user = LoginHelper.getRequiredLoginUser();
    
    List<SomeVO> result = rpcCallLogger.logAndExecute(
            "tyt-goods-service",           // 服务名称
            "methodName",                  // 方法名称
            "userId=" + user.getUserId(),  // 参数信息（可选）
            () -> yourRemoteService.methodName(user.getUserId())  // 实际的RPC调用
    );
    
    return WebResult.success(result);
}
```

#### 无返回值的调用：
```java
@PostMapping("/delete")
public WebResult<Void> delete(@RequestParam Long id) {
    rpcCallLogger.logAndExecute(
            "tyt-goods-service", 
            "delete", 
            "id=" + id,
            () -> yourRemoteService.delete(id)
    );
    return WebResult.success();
}
```

## 日志输出示例

当你调用RPC接口时，会看到类似以下的日志输出：

```
=== RPC调用开始 ===
服务名称: tyt-goods-service
方法名称: getStartLocationHistory
调用参数: userId=12345
发现可用服务实例数量: 2
实例[1] - 主机: 192.168.1.100, 端口: 8080, 实例ID: instance-1, 完整地址: http://192.168.1.100:8080
实例[1]元数据: {version=1.0.0, zone=default}
实例[2] - 主机: 192.168.1.101, 端口: 8080, 实例ID: instance-2, 完整地址: http://192.168.1.101:8080
实例[2]元数据: {version=1.0.0, zone=default}
=== RPC调用成功 ===
服务名称: tyt-goods-service
方法名称: getStartLocationHistory
调用耗时: 150ms
返回类型: ArrayList
返回数量: 5
```

## 已实现的示例

在 `PublishHistoryController` 中已经实现了完整的示例：

1. `getStartLocationHistory()` - 查询出发地历史记录
2. `getDestLocationHistory()` - 查询目的地历史记录  
3. `getTaskContentHistory()` - 查询货物历史记录
4. `delete()` - 删除历史记录

## 优势

1. **详细的实例信息**：可以看到负载均衡后实际调用了哪个服务实例
2. **完整的调用链路**：从请求开始到结束的完整信息
3. **性能监控**：记录每次调用的耗时
4. **错误追踪**：异常情况下的详细错误信息
5. **灵活使用**：可以选择性地在需要的接口上使用

## 注意事项

1. 确保服务注册到了服务发现中心（如Nacos）
2. 日志级别需要设置为debug或更低级别才能看到详细信息
3. 参数信息会被记录到日志中，注意不要包含敏感信息
4. 对于高频调用的接口，考虑日志对性能的影响
