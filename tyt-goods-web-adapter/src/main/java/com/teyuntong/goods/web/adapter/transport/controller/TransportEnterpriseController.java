package com.teyuntong.goods.web.adapter.transport.controller;

import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.transport.vo.InvoiceXHLConsigneeDataVO;
import com.teyuntong.goods.web.service.remote.enterprise.TransportEnterpriseLogRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/transportEnterpriseLog")
@Slf4j
public class TransportEnterpriseController {

    private final TransportEnterpriseLogRemoteService transportEnterpriseLogRemoteService;

    @GetMapping("/data/getXHLConsigneeData")
    @ResponseBody
    public WebResult getXHLConsigneeData() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success();
        }
        log.info("获取最后一单货源的开票主体ID：{}", JSONObject.toJSONString(loginUser.getUserId()));
        List<InvoiceXHLConsigneeDataVO> xhlConsigneeData = transportEnterpriseLogRemoteService.getXHLConsigneeData(loginUser.getUserId());
        return WebResult.success(xhlConsigneeData);
    }

}
