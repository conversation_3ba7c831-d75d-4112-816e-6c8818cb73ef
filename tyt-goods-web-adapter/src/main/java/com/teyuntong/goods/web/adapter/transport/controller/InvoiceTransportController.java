package com.teyuntong.goods.web.adapter.transport.controller;

import com.teyuntong.goods.service.client.invoice.vo.HbwjInvoiceTransportOverrunCheckReq;
import com.teyuntong.goods.service.client.invoice.vo.InvoiceAdditionalPriceVO;
import com.teyuntong.goods.service.client.invoice.vo.InvoiceTransportConfigLogVO;
import com.teyuntong.goods.service.client.invoice.vo.PublishTransportInvoiceVo;
import com.teyuntong.goods.web.service.biz.transport.dto.InvoiceTransportOverrunCheckReq;
import com.teyuntong.goods.web.service.remote.invoice.InvoiceConfigRemoteService;
import com.teyuntong.goods.web.service.remote.invoice.InvoiceTransportRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;


@RequiredArgsConstructor
@RestController
@RequestMapping("/invoiceTransport")
@Slf4j
public class InvoiceTransportController {

    private final InvoiceTransportRemoteService invoiceTransportRemoteService;
    private final InvoiceConfigRemoteService invoiceConfigRemoteService;

    @PostMapping("/check/invoiceTransportOverrunCheck")
    public WebResult<Boolean> invoiceTransportOverrunCheck(@RequestBody InvoiceTransportOverrunCheckReq invoiceTransportOverrunCheckReq) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success(true);
        }

        if (invoiceTransportOverrunCheckReq == null
                || StringUtils.isBlank(invoiceTransportOverrunCheckReq.getServiceProviderCode()) || !invoiceTransportOverrunCheckReq.getServiceProviderCode().equals("HBWJ")
                || invoiceTransportOverrunCheckReq.getDistance() == null
                || invoiceTransportOverrunCheckReq.getWeight() == null
                || invoiceTransportOverrunCheckReq.getPrice() == null) {
            return WebResult.success(true);
        }

        HbwjInvoiceTransportOverrunCheckReq hbwjInvoiceTransportOverrunCheckReq = new HbwjInvoiceTransportOverrunCheckReq();
        BeanUtils.copyProperties(invoiceTransportOverrunCheckReq, hbwjInvoiceTransportOverrunCheckReq);
        return WebResult.success(invoiceTransportRemoteService.hbwjInvoiceTransportOverrunCheck(hbwjInvoiceTransportOverrunCheckReq));
    }

    @GetMapping(value = "/checkUserCanPublishInvoiceTransport")
    public WebResult<BigDecimal> checkUserCanPublishInvoiceTransport(@RequestParam(value = "invoiceSubjectId", required = false) Long invoiceSubjectId) {
        return WebResult.success(invoiceConfigRemoteService.checkUserCanPublishInvoiceTransport(invoiceSubjectId));
    }

    @GetMapping(value = "/getAdditionalPriceAndEnterpriseTaxRate")
    public WebResult<InvoiceAdditionalPriceVO> getAdditionalPriceAndEnterpriseTaxRate(@RequestParam(value = "price", required = false) String price
            , @RequestParam(value = "invoiceSubjectId", required = false) Long invoiceSubjectId) {
        return WebResult.success(invoiceConfigRemoteService.getAdditionalPriceAndEnterpriseTaxRate(price, invoiceSubjectId));
    }

    @GetMapping(value = "/getInvoiceTransportConfig")
    public WebResult<InvoiceTransportConfigLogVO> getInvoiceTransportConfig(@RequestParam(value = "serviceProviderCode", required = false) String serviceProviderCode) {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        return WebResult.success(invoiceConfigRemoteService.getLastInvoiceTransportEnterpriseConfig(userId, serviceProviderCode));
    }

    @GetMapping(value = "/isInvoiceTransport")
    public WebResult<Boolean> isInvoiceTransport(@RequestParam(value = "srcMsgId", required = false) Long srcMsgId) {
        if (srcMsgId == null) {
            return WebResult.success(false);
        }
        return WebResult.success(invoiceConfigRemoteService.isInvoiceTransport(srcMsgId));
    }

    @PostMapping(value = "/getPublishTransportInvoice")
    public WebResult<PublishTransportInvoiceVo> getPublishTransportInvoice() {
        return WebResult.success(invoiceTransportRemoteService.getPublishTransportInvoice());
    }

}
