package com.teyuntong.goods.web.adapter.transport.controller;

import com.teyuntong.goods.service.client.publish.CarouselGoodsDTO;
import com.teyuntong.goods.service.client.publish.dto.*;
import com.teyuntong.goods.service.client.publish.vo.CarouselGoodsVO;
import com.teyuntong.goods.service.client.publish.vo.DriverAssignPopupVO;
import com.teyuntong.goods.service.client.publish.vo.PriceAssistantVO;
import com.teyuntong.goods.service.client.publish.vo.PublishGoodsTypeResultVO;
import com.teyuntong.goods.service.client.transport.dto.*;
import com.teyuntong.goods.service.client.transport.vo.*;
import com.teyuntong.goods.web.service.biz.publish.dto.AutoResendShowDTO;
import com.teyuntong.goods.web.service.biz.publish.service.GoodsPublishService;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 货源发布相关接口
 *
 * <AUTHOR>
 * @since 2024/12/12 14:35
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/goods/publish")
public class GoodsPublishController {

    private final GoodsPublishService goodsPublishService;

    /**
     * 货源发布（首次发布、变价发布）
     */
    @PostMapping(value = "/saveTransport")
    public WebResult<TransportPublishVO> saveTransport(@RequestBody PublishDTO publishDTO) {

        return WebResult.success(goodsPublishService.saveTransport(publishDTO));
    }

    /**
     * 直接发布
     */
    @PostMapping(value = "/directPublish")
    public WebResult<DirectPublishResultVO> directPublish(@RequestBody DirectPublishDTO publishDTO) {
        return WebResult.success(goodsPublishService.directPublish(publishDTO));
    }

    /**
     * 智能订金
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/intelligentDeposit")
    public WebResult<DepositAndRefundTypeVO> intelligentDeposit(@RequestBody IntelligentDepositDTO dto) {
        return WebResult.success(goodsPublishService.intelligentDeposit(dto));
    }

    /**
     * 保存智能订金用户操作记录
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/depositRecord")
    public WebResult depositRecord(@RequestBody IntelligentDepositDTO dto) {
        goodsPublishService.depositRecord(dto);
        return WebResult.success();
    }

    /**
     * 是否展示回价助手
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/showPriceAssistant")
    public WebResult<PriceAssistantVO> showPriceAssistant(@RequestBody PriceAssistantDTO dto) {
        return WebResult.success(goodsPublishService.showPriceAssistant(dto));
    }

    /**
     * 保存回价助手信息
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/savePriceAssistant")
    public WebResult savePriceAssistant(@RequestBody PriceAssistantDTO dto) {
        return WebResult.success(goodsPublishService.savePriceAssistant(dto));
    }

    /**
     * 保存回价助手信息
     *
     * @return
     */
    @GetMapping(value = "/clearRedPoint")
    public WebResult clearRedPoint() {
        return WebResult.success(goodsPublishService.clearRedPoint());
    }

    @PostMapping("/getSpecialCarInfo")
    public WebResult getSpecialCarInfo(@RequestBody SpecialCarInfoDTO dto) {
        return WebResult.success(goodsPublishService.getSpecialCarInfo(dto));
    }

    /**
     * 获取预计成交时长
     * 专车相关信息展示：
     *  1.是否展示司机驾驶此类货物
     *  2.是否隐藏装车联系电话、卸货联系电话、公里数、是否包含其他费用及对应校验
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/getDealMinutes")
    public WebResult getDealMinutes(@RequestBody DealMinutesDTO dto) {
        return WebResult.success(goodsPublishService.getDealMinutes(dto));
    }

    /**
     * 曝光
     */
    @PostMapping(value = "/rePublish")
    public WebResult<DirectPublishResultVO> rePublish(@RequestBody DirectPublishDTO publishDTO) {
        DirectPublishResultVO resultVO = goodsPublishService.rePublish(publishDTO);
        PublishNoticeDataVO noticeData = resultVO.getNoticeData();
        resultVO.setNoticeData(null);
        return WebResult.success(resultVO, noticeData);
    }

    /**
     * 填价/加价接口
     */
    @PostMapping(value = "/updatePrice")
    public WebResult<DirectPublishResultVO> updatePrice(@RequestBody DirectPublishDTO publishDTO) {
        DirectPublishResultVO resultVO = goodsPublishService.updatePrice(publishDTO);
        return WebResult.success(resultVO);
    }

    /**
     * 转电议/一口价接口
     */
    @PostMapping(value = "/transfer")
    public WebResult<DirectPublishResultVO> transfer(@RequestBody DirectPublishDTO publishDTO) {
        DirectPublishResultVO resultVO = goodsPublishService.transfer(publishDTO);
        return WebResult.success(resultVO);
    }

    /**
     * 更新货源信息
     */
    @PostMapping(value = "/updateGoodsInfo")
    public WebResult<DirectPublishResultVO> updateGoodsInfo(@RequestBody UpdateGoodsInfoDTO updateGoodsInfoDTO) {
        DirectPublishResultVO resultVO = goodsPublishService.updateGoodsInfo(updateGoodsInfoDTO);
        return WebResult.success(resultVO);
    }

    /**
     * 校验是否可以加价
     */
    @GetMapping(value = "/checkAddPrice")
    public WebResult<DirectPublishResultVO> checkAddPrice(@RequestParam Long srcMsgId) {
        return WebResult.success();
    }


    /**
     * 发货页是否展示自动重发
     */
    @GetMapping(value = "/showAutoResend")
    public WebResult<AutoResendShowDTO> showAutoResend() {
        AutoResendShowDTO showDTO = goodsPublishService.showAutoResend();
        return WebResult.success(showDTO);
    }

    /**
     * 我的货源页点击直接发布是否弹出出价接口
     */
    @GetMapping(value = "/isPopupPriceBox")
    public WebResult<PopUpPriceBoxDTO> isPopupPriceBox(@RequestParam Long srcMsgId) {
        return WebResult.success(goodsPublishService.isPopupPriceBox(srcMsgId));
    }

    /**
     * 直接发布时是否展示升级为优车发货弹窗
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping(value = "/showExcellentPricePopup")
    public WebResult<UpgradeExcellentDTO> showExcellentPricePopup(@RequestParam Long srcMsgId) {
        return WebResult.success(goodsPublishService.showExcellentPricePopup(srcMsgId));
    }

    /**
     * 我的货源页点击撤销是否弹出挽留弹窗接口
     */
    @GetMapping(value = "/showRetention")
    public WebResult<ShowRetentionDTO> showRetention(@RequestParam Long srcMsgId) {
        return WebResult.success(goodsPublishService.showRetention(srcMsgId));
    }

    /**
     * 我的货源页点击撤销是否弹出挽留弹窗接口
     */
    @PostMapping(value = "/publishPopupPriceBox")
    public WebResult<PopUpPriceBoxDTO> publishPopupPriceBox(@RequestBody TransportPublishDTO publishDTO) {
        return WebResult.success(goodsPublishService.publishPopupPriceBox(publishDTO));
    }

    /**
     * 货源发布、变更时弹窗埋点记录接口
     */
    @PostMapping(value = "/popup/tracking")
    public WebResult<Void> popupTracking(@RequestBody PopupTrackingLogDTO publishDTO) {
        goodsPublishService.popupTracking(publishDTO);
        return WebResult.success();
    }

    /**
     * 获取货源发布类型
     * @param publishGoodsTypeDTO 入参
     * @return
     */
    @PostMapping(value = "/getPublishGoodsType")
    public WebResult<PublishGoodsTypeResultVO> getPublishGoodsType(@RequestBody PublishGoodsTypeDTO publishGoodsTypeDTO){
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        publishGoodsTypeDTO.setUserId(loginUser.getUserId());
        BaseParamDTO getBaseParam = LoginHelper.getBaseParam();
        publishGoodsTypeDTO.setClientVersion(getBaseParam.getClientVersion());
        return WebResult.success(goodsPublishService.getPublishGoodsType(publishGoodsTypeDTO));
    }

    /**
     * 计算专车运费
     * @param priceDTO
     * @return
     */
    @PostMapping(value = "/calcSpecialGoodsPrice")
    public WebResult<CalcSpecialGoodsPriceResultDTO> calcSpecialGoodsPrice(@RequestBody CalculatePriceDTO priceDTO){
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        priceDTO.setUserId(loginUser.getUserId());
        BaseParamDTO getBaseParam = LoginHelper.getBaseParam();
        priceDTO.setClientVersion(getBaseParam.getClientVersion());
        return WebResult.success(goodsPublishService.calcSpecialGoodsPrice(priceDTO));
    }

    /**
     * 检测发布下一步
     * @param nextStepDTO
     * @return
     */
    @PostMapping(value = "/checkPublishNextStep")
    public WebResult<Boolean> checkPublishNextStep(@RequestBody CheckNextStepDTO nextStepDTO){
        return WebResult.success(goodsPublishService.checkPublishNextStep(nextStepDTO));
    }

    /**
     * 运费出价校验
     * @param nextStepDTO
     * @return
     */
    @PostMapping(value = "/checkPrice")
    public WebResult<Boolean> checkPrice(@RequestBody CheckNextStepDTO nextStepDTO){
        return WebResult.success(goodsPublishService.checkPrice(nextStepDTO));
    }

    /**
     * 获取下一步轮播货源数据
     * @param carouselGoodsDTO
     * @return
     */
    @PostMapping(value = "/getCarouselGoods")
    public WebResult<List<CarouselGoodsVO>> getCarouselGoods(@RequestBody CarouselGoodsDTO carouselGoodsDTO){
        return WebResult.success(goodsPublishService.getCarouselGoods(carouselGoodsDTO));
    }

    /**
     * 获取发布货源类型标签
     * @param publishGoodsTypeLabelDTO
     * @return
     */
    @PostMapping(value = "/getPublishGoodsTypeLabel")
    public WebResult<List<PublishGoodsTypeLabelVO>> getPublishGoodsTypeLabel(@RequestBody PublishGoodsTypeLabelDTO publishGoodsTypeLabelDTO){
        return WebResult.success(goodsPublishService.getPublishGoodsTypeLabel(publishGoodsTypeLabelDTO));
    }


    /**T
     * 货源发布、变更时弹窗埋点记录接口T
     */
    @PostMapping(value = "/driverAssignPopup")
    public WebResult<DriverAssignPopupVO> driverAssignPopup(@RequestBody DriverAssignPopupDTO driverAssignPopupDTO) {
        return WebResult.success(goodsPublishService.driverAssignPopup(driverAssignPopupDTO));
    }

}
