package com.teyuntong.goods.web.adapter.transport.controller;

import com.teyuntong.goods.service.client.transport.vo.PublishLocationHistoryVO;
import com.teyuntong.goods.service.client.transport.vo.PublishTaskContentHistoryVO;
import com.teyuntong.goods.web.service.common.util.RpcCallLogger;
import com.teyuntong.goods.web.service.remote.publish.PublishHistoryRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;


@RequiredArgsConstructor
@RestController
@RequestMapping("/publishHistory")
@Slf4j
public class PublishHistoryController {

    private final PublishHistoryRemoteService PublishHistoryRemoteService;
    private final RpcCallLogger rpcCallLogger;

    /**
     * 查询用户出发地历史记录（近半年）
     *
     * @return 出发地历史记录列表
     */
    @GetMapping("/startPoint")
    public WebResult<List<PublishLocationHistoryVO>> getStartLocationHistory() {
        LoginUserDTO requiredLoginUser = LoginHelper.getRequiredLoginUser();

        // 使用RpcCallLogger记录详细的RPC调用信息
        List<PublishLocationHistoryVO> startLocationHistory = rpcCallLogger.logAndExecute(
                "tyt-goods-service",
                "getStartLocationHistory",
                "userId=" + requiredLoginUser.getUserId(),
                () -> PublishHistoryRemoteService.getStartLocationHistory(requiredLoginUser.getUserId())
        );

        return WebResult.success(startLocationHistory);
    }

    /**
     * 查询用户目的地历史记录（近半年）
     *
     * @return 目的地历史记录列表
     */
    @GetMapping("/destPoint")
    public WebResult<List<PublishLocationHistoryVO>> getDestLocationHistory() {
        LoginUserDTO requiredLoginUser = LoginHelper.getRequiredLoginUser();

        List<PublishLocationHistoryVO> destLocationHistory = rpcCallLogger.logAndExecute(
                "tyt-goods-service",
                "getDestLocationHistory",
                "userId=" + requiredLoginUser.getUserId(),
                () -> PublishHistoryRemoteService.getDestLocationHistory(requiredLoginUser.getUserId())
        );

        return WebResult.success(destLocationHistory);
    }

    /**
     * 查询用户货物历史记录（近半年）
     *
     * @return 货物历史记录列表
     */
    @GetMapping("/taskContent")
    public WebResult<List<PublishTaskContentHistoryVO>> getTaskContentHistory() {
        LoginUserDTO requiredLoginUser = LoginHelper.getRequiredLoginUser();

        List<PublishTaskContentHistoryVO> taskContentHistory = rpcCallLogger.logAndExecute(
                "tyt-goods-service",
                "getTaskContentHistory",
                "userId=" + requiredLoginUser.getUserId(),
                () -> PublishHistoryRemoteService.getTaskContentHistory(requiredLoginUser.getUserId())
        );

        return WebResult.success(taskContentHistory);
    }

    @GetMapping("/delete")
    public WebResult<Void> delete(@RequestParam("id") Long id, @RequestParam("type") Integer type) {
        rpcCallLogger.logAndExecute(
                "tyt-goods-service",
                "delete",
                "id=" + id + ", type=" + type,
                () -> PublishHistoryRemoteService.delete(id, type)
        );
        return WebResult.success();
    }

}
