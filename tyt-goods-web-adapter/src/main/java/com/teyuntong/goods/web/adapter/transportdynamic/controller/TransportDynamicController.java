package com.teyuntong.goods.web.adapter.transportdynamic.controller;

import com.teyuntong.goods.service.client.transport.vo.TransportDynamicVO;
import com.teyuntong.goods.web.service.biz.transportdynamic.service.TransportDynamicService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/goods/transportdynamic")
public class TransportDynamicController {

    private final TransportDynamicService transportDynamicService;

    @GetMapping("/getTransportDynamic")
    public WebResult<TransportDynamicVO> getTransportNoLookOverQuotedPriceCountNum() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            TransportDynamicVO transportDynamicVO = new TransportDynamicVO();
            transportDynamicVO.setShowTransportDynamicTab(false);
            return WebResult.success(transportDynamicVO);
        }
        return WebResult.success(transportDynamicService.getTransportDynamic(loginUser.getUserId()));
    }

}
