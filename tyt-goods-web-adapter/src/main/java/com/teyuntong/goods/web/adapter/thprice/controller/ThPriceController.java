package com.teyuntong.goods.web.adapter.thprice.controller;

import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.TecServiceFeeVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.web.service.biz.thprice.service.ThPriceService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/price/thPrice")
public class ThPriceController {

    private final ThPriceService thPriceService;

    @PostMapping("/getThPrice")
    public WebResult<CarryPriceVO> getThPrice(@RequestBody TransportCarryReq transportCarryReq) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success(null);
        }
        return WebResult.success(thPriceService.getThPrice(transportCarryReq));
    }

    /**
     * 获取技术服务费建议价
     * @param publishDTO
     * @return
     */
    @PostMapping("/tecServiceFee")
    public WebResult<TecServiceFeeVO> tecServiceFee(@RequestBody PublishDTO publishDTO) {
        return WebResult.success(thPriceService.tecServiceFee(publishDTO));
    }

}
