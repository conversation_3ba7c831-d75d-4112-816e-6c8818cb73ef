package com.teyuntong.goods.web.adapter.goodsmatch.controller;

import com.teyuntong.goods.service.client.goodsname.dto.CheckScreeningWordDto;
import com.teyuntong.goods.service.client.goodsname.dto.GoodsMatchRpcDto;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsBrandListVo;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsMatchVo;
import com.teyuntong.goods.web.service.biz.goodsmatch.service.GoodsMatchService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2024/4/23 13:31
 */

@RequiredArgsConstructor
@RestController
@RequestMapping("/goods/match")
public class GoodsMatchController {


    private final GoodsMatchService goodsMatchService;

    /**
     * 货名搜索
     *
     * @param goodsMatchRpcDto
     * @return
     */
    @PostMapping("/list")
    public WebResult<GoodsMatchVo> goodsMatch(@RequestBody @Validated GoodsMatchRpcDto goodsMatchRpcDto) {
        return WebResult.success(goodsMatchService.goodsMatch(goodsMatchRpcDto));
    }

    /**
     * 校验敏感词
     *
     * @param checkScreeningWordDto
     * @return
     */
    @PostMapping("/screeningWordCheck")
    public WebResult screeningWordCheck(@RequestBody @Validated CheckScreeningWordDto checkScreeningWordDto) {
        goodsMatchService.screeningWordCheck(checkScreeningWordDto);
        return WebResult.success();
    }

    /**
     * 判断货源是否可编辑
     * @param srcMsgId
     * @param type 1撤销 2在线编辑
     * @return
     */
    @GetMapping(value = "/isEdit")
    public WebResult isEdit(@RequestParam("srcMsgId") Long srcMsgId, @RequestParam(value = "type") Integer type){
        return WebResult.success(goodsMatchService.isEdit(srcMsgId, type));
    }

    /**
     * 货名搜索品牌型号
     *
     * @param goodsMatchRpcDto
     * @return
     */
    @PostMapping("/brand")
    public WebResult<GoodsBrandListVo> goodsBrand(@RequestBody @Validated GoodsMatchRpcDto goodsMatchRpcDto) {
        return WebResult.success(goodsMatchService.goodsBrand(goodsMatchRpcDto));
    }

}
