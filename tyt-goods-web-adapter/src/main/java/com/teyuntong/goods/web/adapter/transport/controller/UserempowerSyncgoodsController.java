package com.teyuntong.goods.web.adapter.transport.controller;

import com.teyuntong.goods.service.client.publish.dto.UserempowerSyncgoodsConsentDTO;
import com.teyuntong.goods.service.client.publish.dto.UserempowerSyncgoodsDTO;
import com.teyuntong.goods.service.client.publish.dto.UserempowerSyncgoodsRejectDTO;
import com.teyuntong.goods.web.service.common.error.TytGoodsWebErrorCode;
import com.teyuntong.goods.web.service.remote.goodsname.UserempowerSyncgoodsRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/userempower/syncgoods")
@Slf4j
public class UserempowerSyncgoodsController {

    private final UserempowerSyncgoodsRemoteService userempowerSyncgoodsRemoteService;

    /**
     * 同步货源授权弹窗数据接口
     *
     * @return 授权弹窗数据
     */
    @GetMapping("/popup")
    WebResult<UserempowerSyncgoodsDTO> getSyncgoodsPopupData(@RequestParam(value = "srcMsgId", required = false) Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == null) {
            return WebResult.error(TytGoodsWebErrorCode.PARAM_ERROR);
        }
        return WebResult.success(userempowerSyncgoodsRemoteService.getSyncgoodsPopupData(loginUser.getUserId(), srcMsgId));
    }

    /**
     * 同意货源授权接口
     *
     * @return 是否成功
     */
    @GetMapping("/consent")
    WebResult<Boolean> consentSyncgoodsAuth(@RequestParam(name = "srcMsgId", required = false) Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == null || StringUtils.isBlank(loginUser.getTrueName())) {
            return WebResult.error(TytGoodsWebErrorCode.PARAM_ERROR);
        }
        UserempowerSyncgoodsConsentDTO userempowerSyncgoodsConsentDTO = new UserempowerSyncgoodsConsentDTO();
        userempowerSyncgoodsConsentDTO.setUserId(loginUser.getUserId());
        userempowerSyncgoodsConsentDTO.setGoodsUserName(loginUser.getTrueName());
        userempowerSyncgoodsConsentDTO.setSrcMsgId(srcMsgId);
        return WebResult.success(userempowerSyncgoodsRemoteService.consentSyncgoodsAuth(userempowerSyncgoodsConsentDTO));
    }

    /**
     * 拒绝货源授权接口
     *
     * @return 是否成功
     */
    @GetMapping("/reject")
    WebResult<Boolean> rejectSyncgoodsAuth() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == null) {
            return WebResult.error(TytGoodsWebErrorCode.PARAM_ERROR);
        }
        UserempowerSyncgoodsRejectDTO userempowerSyncgoodsRejectDTO = new UserempowerSyncgoodsRejectDTO();
        userempowerSyncgoodsRejectDTO.setUserId(loginUser.getUserId());
        return WebResult.success(userempowerSyncgoodsRemoteService.rejectSyncgoodsAuth(userempowerSyncgoodsRejectDTO));
    }
}
