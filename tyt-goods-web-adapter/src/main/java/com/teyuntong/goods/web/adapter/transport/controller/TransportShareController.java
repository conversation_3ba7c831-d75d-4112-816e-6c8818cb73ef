package com.teyuntong.goods.web.adapter.transport.controller;

import com.teyuntong.goods.service.client.transport.dto.TransportShareDTO;
import com.teyuntong.goods.web.service.biz.transport.service.TransportShareService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 货源分享相关
 *
 * <AUTHOR>
 * @since 2025-06-19 15:17
 */
@RestController
@RequestMapping("/transport/share")
@Slf4j
public class TransportShareController {

    @Resource
    private TransportShareService transportShareService;

    /**
     * 保存货源分享记录
     *
     * @param dto
     * @return
     */
    @PostMapping("/add")
    public WebResult<Boolean> add(@RequestBody TransportShareDTO dto) {
        return WebResult.success(transportShareService.add(dto));
    }
}
