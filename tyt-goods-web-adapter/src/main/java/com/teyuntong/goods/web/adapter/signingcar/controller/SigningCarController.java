package com.teyuntong.goods.web.adapter.signingcar.controller;

import com.teyuntong.goods.service.client.specialcar.vo.InvoiceDriverVO;
import com.teyuntong.goods.service.client.specialcar.vo.UserCarVO;
import com.teyuntong.goods.web.service.remote.signingcar.SigningCarRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/06/30 17:34
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/signingCar")
public class SigningCarController {

    private final SigningCarRemoteService signingCarRemoteService;

    @GetMapping(value = "/updateCarStatusByUserId")
    public WebResult<Boolean> updateCarStatusByUserId(@RequestParam("status") Integer status){
        LoginUserDTO requiredLoginUser = LoginHelper.getRequiredLoginUser();
        signingCarRemoteService.updateCarStatusByUserId(requiredLoginUser.getUserId(),status);
        return WebResult.success();
    }


    @GetMapping(value = "/getSigningCarBlack")
    public WebResult<Boolean> getSigningCarBlack(){
        LoginUserDTO requiredLoginUser = LoginHelper.getRequiredLoginUser();
        return WebResult.success(signingCarRemoteService.getSigningCarBlack(requiredLoginUser.getUserId()));
    }

    @GetMapping(value = "/checkCarDriver")
    public WebResult<Boolean> checkCarDriver(@RequestParam("id") Long id,
                                             @RequestParam("carId") Long carId, @RequestParam("driverId")Long driverId){

        LoginUserDTO requiredLoginUser = LoginHelper.getRequiredLoginUser();
        return WebResult.success(signingCarRemoteService.checkCarDriver(requiredLoginUser.getUserId(),id,carId,driverId));
    }

    @GetMapping(value = "/getUserSigningCarList")
    public WebResult<Map<String, List<UserCarVO>>> getUserSigningCarList(){
        LoginUserDTO requiredLoginUser = LoginHelper.getRequiredLoginUser();
        return WebResult.success(signingCarRemoteService.getSigningCarList(requiredLoginUser.getUserId()));
    }

    @GetMapping(value = "/getSigningUserList")
    public WebResult<Map<String,List<InvoiceDriverVO>>> getSigningUserList(@RequestParam("id") Long id){
        LoginUserDTO requiredLoginUser = LoginHelper.getRequiredLoginUser();
        return WebResult.success(signingCarRemoteService.getSigningUserList(requiredLoginUser.getUserId(), id));
    }


}
