package com.teyuntong.goods.web.adapter.transport.controller;

import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.transport.dto.*;
import com.teyuntong.goods.service.client.transport.enums.QueryMenuTypeEnum;
import com.teyuntong.goods.service.client.transport.vo.SimilarityTransportTurnoverRatioVO;
import com.teyuntong.goods.web.service.biz.transport.service.TransportService;
import com.teyuntong.goods.web.service.common.error.PublishCommonCheckErrorCode;
import com.teyuntong.goods.web.service.common.error.TytGoodsWebErrorCode;
import com.teyuntong.goods.web.service.remote.goodsname.TransportMainRemoteService;
import com.teyuntong.goods.web.service.remote.goodsname.TransportStatusRemoteService;
import com.teyuntong.goods.web.service.remote.invoice.InvoiceConfigRemoteService;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


@RequiredArgsConstructor
@RestController
@RequestMapping("/goods")
@Slf4j
public class TransportController {

    private final TransportMainRemoteService transportMainRemoteService;

    private final TransportStatusRemoteService transportStatusRemoteService;

    private final TransportService transportService;

    private final InvoiceConfigRemoteService invoiceConfigRemoteService;

    @GetMapping("/data/getLastTransportInvoiceSubjectId")
    @ResponseBody
    public WebResult getLastTransportInvoiceSubjectId() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success();
        }
        log.info("获取最后一单货源的开票主体ID：{}", JSONObject.toJSONString(loginUser.getUserId()));
        Long lastTransportInvoiceSubjectId = transportMainRemoteService.getLastTransportInvoiceSubjectId(loginUser.getUserId());
        return WebResult.success(lastTransportInvoiceSubjectId);
    }

    @PostMapping("/getMyPublish")
    public WebResult getMyPublish(@RequestBody TransportListDTO dto) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success();
        }
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        dto.setUserId(loginUser.getUserId());
        if (Objects.nonNull(baseParam) && Objects.nonNull(baseParam.getClientSign())) {
            dto.setClientSign(baseParam.getClientSign());
        }
        if (Objects.isNull(dto.getQueryMenuType()) || !QueryMenuTypeEnum.legalType(dto.getQueryMenuType())) {
            log.info("getMyPublish 非法参数queryMenuType {}", JSONObject.toJSONString(dto));
            return WebResult.success();
        }
        if (Objects.isNull(dto.getQueryID()) || dto.getQueryID() < 0) {
            log.info("getMyPublish 非法参数queryId {}", JSONObject.toJSONString(dto));
            return WebResult.success();
        }
        if (Objects.isNull(dto.getQueryActionType()) || dto.getQueryActionType() < 1 || dto.getQueryActionType() > 2) {
            log.info("getMyPublish 非法参数queryActionType {}", JSONObject.toJSONString(dto));
            return WebResult.success();
        }
        return WebResult.success(transportService.getMyPublish(dto));
    }

    /**
     * 发货锚点和发货方式
     *
     * @return
     */
    @PostMapping("/getGoodsPoint")
    public WebResult getGoodsPoint(@RequestBody GoodsPointDTO dto) {
        return WebResult.success(transportService.getGoodsPoint(dto));
    }

    /**
     * 获取货源撤销原因列表
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/cancelReasonList")
    public WebResult cancelReasonList(@RequestParam("srcMsgId") Long srcMsgId) {
        return WebResult.success(transportService.cancelReasonList(srcMsgId));
    }

    /**
     * 刷新货源
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/refresh")
    public WebResult refresh(@RequestParam("srcMsgId") Long srcMsgId) {
        transportService.refresh(srcMsgId);
        return WebResult.success();
    }

    @PostMapping("/data/getSimilarityCount")
    public WebResult getSimilarityCount(@RequestBody TransportSimilarityDTO similarityDTO) {
        int similarityCount = transportMainRemoteService.getSimilarityCount(similarityDTO);
        SimilarityTransportTurnoverRatioVO similarityTransportTurnoverRatio = transportMainRemoteService.getSimilarityTransportTurnoverRatio(similarityDTO);

        Map<String, Object> map = new HashMap<>();
        map.put("similarityCount", similarityCount);
        if (similarityCount > 0) {
            map.put("prompt", "您的货与" + similarityCount + "票货高度相似，<span style=\"color:#FF5B00;\">填价后优先展示</span>");
        } else {
            if (similarityTransportTurnoverRatio.getLowRatio() != null && similarityTransportTurnoverRatio.getLowRatio().compareTo(BigDecimal.ZERO) > 0
                    && similarityTransportTurnoverRatio.getHighRatio() != null && similarityTransportTurnoverRatio.getHighRatio().compareTo(BigDecimal.ZERO) > 0) {
                map.put("prompt", "该路线无价货源找车成功率为" + similarityTransportTurnoverRatio.getLowRatio().setScale(0, RoundingMode.DOWN).toPlainString()
                        + "%，<span style=\"color:#FF5B00;\">填价后提升至" + similarityTransportTurnoverRatio.getHighRatio().setScale(0, RoundingMode.DOWN).toPlainString() + "%</span>");
            }
        }
        if (similarityTransportTurnoverRatio.getHighRatio() != null && similarityTransportTurnoverRatio.getHighRatio().compareTo(BigDecimal.ZERO) > 0) {
            map.put("highPrompt", "填写合理运费，<span style=\"color:#FF5B00;\">找车成功率可达" + similarityTransportTurnoverRatio.getHighRatio().setScale(0, RoundingMode.DOWN).toPlainString() + "%</span>");
        }
        if (similarityTransportTurnoverRatio.getHighestRatio() != null && similarityTransportTurnoverRatio.getHighestRatio().compareTo(BigDecimal.ZERO) > 0) {
            map.put("highestPrompt", "当前为一口价，<span style=\"color:#FF5B00;\">找车成功率可达" + similarityTransportTurnoverRatio.getHighestRatio().setScale(0, RoundingMode.DOWN).toPlainString() + "%</span>");
        }
        return WebResult.success(map);
    }

    @PostMapping("/check/publishNextPageCommonCheckData")
    public WebResult publishNextPageCommonCheckData(@RequestBody PublishNextPageCommonCheckDTO publishNextPageCommonCheckDTO) {
        if (publishNextPageCommonCheckDTO == null) {
            return WebResult.error(TytGoodsWebErrorCode.BUSINESS_ERROR);
        }

        //开票货源校验
        if (Boolean.TRUE.equals(publishNextPageCommonCheckDTO.getInvoiceTransport())) {
            if (publishNextPageCommonCheckDTO.getDistance() == null || publishNextPageCommonCheckDTO.getDistance().compareTo(BigDecimal.ZERO) < 0) {
                return WebResult.error(PublishCommonCheckErrorCode.DISTANCE_ERROR);
            }
            invoiceConfigRemoteService.checkUserCanPublishInvoiceTransportNextPageBtn(publishNextPageCommonCheckDTO.getInvoiceSubjectId(), publishNextPageCommonCheckDTO.getServiceProviderCode(), publishNextPageCommonCheckDTO.getSpecialCar(), publishNextPageCommonCheckDTO.getDistance());
        }

        return WebResult.success();
    }

    @PostMapping("/check/publishNextPageCommonCheck")
    public WebResult publishNextPageCommonCheck(@RequestBody PublishNextPageCommonCheckDTO publishNextPageCommonCheckDTO) {
        if (publishNextPageCommonCheckDTO == null) {
            return WebResult.error(TytGoodsWebErrorCode.BUSINESS_ERROR);
        }

        //开票货源、专车货源校验距离
        if (Boolean.TRUE.equals(publishNextPageCommonCheckDTO.getInvoiceTransport()) || Boolean.TRUE.equals(publishNextPageCommonCheckDTO.getSpecialCar())) {
            if (publishNextPageCommonCheckDTO.getDistance() == null || publishNextPageCommonCheckDTO.getDistance().compareTo(BigDecimal.ZERO) < 0) {
                return WebResult.error(PublishCommonCheckErrorCode.DISTANCE_ERROR);
            }
        }
        return WebResult.success();
    }

    @PostMapping("/setGoodsBackOut")
    public WebResult setGoodsBackOut(@RequestBody SaveGoodsStatusDTO saveGoodsStatusDTO) {
        if (saveGoodsStatusDTO == null || saveGoodsStatusDTO.getSrcMsgId() == null) {
            return WebResult.error(TytGoodsWebErrorCode.PARAM_ERROR);
        }

        if (saveGoodsStatusDTO.getRequestSource() == null || saveGoodsStatusDTO.getRequestSource() != 1) {
            //非编辑并重发情况下才校验撤销原因入参
            if (saveGoodsStatusDTO.getBackoutReasonValue() == null
                    || StringUtils.isBlank(saveGoodsStatusDTO.getBackoutReasonKey())) {
                return WebResult.error(TytGoodsWebErrorCode.PARAM_ERROR);
            }
        }

        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == null) {
            return WebResult.error(TytGoodsWebErrorCode.PARAM_ERROR);
        }
        saveGoodsStatusDTO.setUserId(loginUser.getUserId());
        transportStatusRemoteService.setGoodsBackOut(saveGoodsStatusDTO);
        return WebResult.success();
    }

    @PostMapping("/setGoodsDone")
    public WebResult setGoodsDone(@RequestBody SaveGoodsStatusDTO saveGoodsStatusDTO) {
        if (saveGoodsStatusDTO == null || saveGoodsStatusDTO.getSrcMsgId() == null
                || saveGoodsStatusDTO.getDoneRequest() == null) {
            return WebResult.error(TytGoodsWebErrorCode.PARAM_ERROR);
        }
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == null) {
            return WebResult.error(TytGoodsWebErrorCode.PARAM_ERROR);
        }
        saveGoodsStatusDTO.setUserId(loginUser.getUserId());
        transportStatusRemoteService.setGoodsDone(saveGoodsStatusDTO);
        return WebResult.success();
    }

    /**
     * 删除我的货源
     */
    @GetMapping("/deleteMyGoods")
    public WebResult<Void> deleteMyGoods(@RequestParam("srcMsgId") Long srcMsgId) {
        transportMainRemoteService.deleteMyGoods(srcMsgId);
        return WebResult.success();
    }

}
