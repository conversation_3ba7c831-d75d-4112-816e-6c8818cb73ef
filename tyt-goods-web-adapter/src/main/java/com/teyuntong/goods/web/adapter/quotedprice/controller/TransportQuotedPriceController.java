package com.teyuntong.goods.web.adapter.quotedprice.controller;

import com.teyuntong.goods.service.client.quotedprice.dto.QuotedPriceDTO;
import com.teyuntong.goods.service.client.quotedprice.vo.*;
import com.teyuntong.goods.service.client.transport.vo.TransportMainVO;
import com.teyuntong.goods.web.service.biz.quotedprice.service.TransportQuotedPriceService;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@RestController
@RequestMapping("/goods/quotedPrice")
public class TransportQuotedPriceController {

    private final TransportQuotedPriceService transportQuotedPriceService;

    @GetMapping({"/getTransportNoLookOverQuotedPriceCountNum", "/getTransportNoLookOverQuotedPriceCountNumNew"})
    public WebResult<Integer> getTransportNoLookOverQuotedPriceCountNum() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success(0);
        }
        return WebResult.success(transportQuotedPriceService.getTransportNoLookOverQuotedPriceCountNum(loginUser.getUserId()));
    }

    /**
     * 货源详情报价列表
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/getQuotedPriceListInSingleDetailPage")
    public WebResult<TransportQuotedPriceDataInDetailPageVO> getQuotedPriceListInSingleDetailPage(@RequestParam("srcMsgId") Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success(new TransportQuotedPriceDataInDetailPageVO());
        }
        return WebResult.success(transportQuotedPriceService.getQuotedPriceListInSingleDetailPage(srcMsgId, loginUser.getUserId()));
    }

    /**
     * 货主同意报价
     *
     * @param agreeDTO
     * @return
     */
    @PostMapping("/transportAgree")
    public WebResult transportAgree(@RequestBody QuotedPriceDTO agreeDTO) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        if (Objects.isNull(agreeDTO.getSrcMsgId()) || Objects.isNull(agreeDTO.getTransportQuotedPriceId())) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        agreeDTO.setUserId(loginUser.getUserId());
        // if (Objects.nonNull(baseParam)) {
        //     agreeDTO.setClientSign(baseParam.getClientSign());
        //     agreeDTO.setClientVersion(baseParam.getClientVersion());
        // }
        return WebResult.success(transportQuotedPriceService.transportAgree(agreeDTO));
    }

    /**
     * 货主出价/拒绝报价
     *
     * @param agreeDTO
     * @return
     */
    @PostMapping("/transportQuotedPrice")
    public WebResult transportQuotedPrice(@RequestBody QuotedPriceDTO agreeDTO) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        if (Objects.isNull(agreeDTO.getSrcMsgId()) || Objects.isNull(agreeDTO.getTransportQuotedPriceId())) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        agreeDTO.setUserId(loginUser.getUserId());
        // if (Objects.nonNull(baseParam)) {
        //     agreeDTO.setClientSign(baseParam.getClientSign());
        //     agreeDTO.setClientVersion(baseParam.getClientVersion());
        // }
        return WebResult.success(transportQuotedPriceService.transportQuotedPrice(agreeDTO));
    }

    /**
     * 货主出价/拒绝报价
     *
     * @param priceDTO
     * @return
     */
    @PostMapping("/carQuotedPrice")
    public WebResult carQuotedPrice(@RequestBody QuotedPriceDTO priceDTO) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        if (Objects.isNull(priceDTO.getSrcMsgId()) || Objects.isNull(priceDTO.getPrice())) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        priceDTO.setUserId(loginUser.getUserId());
        return WebResult.success(transportQuotedPriceService.carQuotedPrice(priceDTO));
    }

    /**
     * 车同意报价
     *
     * @param priceDTO
     * @return
     */
    @PostMapping("/carAgree")
    public WebResult carAgree(@RequestBody QuotedPriceDTO priceDTO) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        if (Objects.isNull(priceDTO.getSrcMsgId())) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        priceDTO.setUserId(loginUser.getUserId());
        return WebResult.success(transportQuotedPriceService.carAgree(priceDTO));
    }

    /**
     * 货报价挽留弹窗
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/getTransportQuotedPriceLeaveTab")
    public WebResult<TransportQuotedPriceLeaveTabVO> getTransportQuotedPriceLeaveTab(@RequestParam("srcMsgId") Long srcMsgId) {
        if (Objects.isNull(srcMsgId)) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        return WebResult.success(transportQuotedPriceService.getTransportQuotedPriceLeaveTab(srcMsgId));
    }

    /**
     * 货报价挽留弹窗
     *
     * @param transportQuotedPriceId
     * @return
     */
    @GetMapping("/getTransportQuotedPriceTabData")
    public WebResult<TransportQuotedPriceTabDataVO> getTransportQuotedPriceTabData(@RequestParam("transportQuotedPriceId") Long transportQuotedPriceId) {
        if (Objects.isNull(transportQuotedPriceId)) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        return WebResult.success(transportQuotedPriceService.getTransportQuotedPriceTabData(transportQuotedPriceId));
    }

    /**
     * 车方 被反馈（报价被货方同意）、有回价气泡内容
     *
     * @return
     */
    @GetMapping("/getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice")
    public WebResult<String> getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice() {
        return WebResult.success(transportQuotedPriceService.getCarHaveNewQuotedPriceOrAgreeQuotedPrice());
    }

    /**
     * 货获取所有发布中货源的所有报价列表
     *
     * @return
     */
    @GetMapping("/getAllPublishingTransportQuotedPriceList")
    public WebResult<List<AllTransportQuotedPriceVO>> getAllPublishingTransportQuotedPriceList() {
        return WebResult.success(transportQuotedPriceService.getAllPublishingTransportQuotedPriceList());
    }

    /**
     * 货方货源详情顶部报价列表点击提醒报价气泡
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/clickTransportQuotedPriceBubble")
    public WebResult<Boolean> clickTransportQuotedPriceBubble(@RequestParam("srcMsgId") Long srcMsgId) {
        return WebResult.success(transportQuotedPriceService.clickTransportQuotedPriceBubble(srcMsgId));
    }

    /**
     * 货获取所有发布中货源的所有报价列表
     *
     * @return
     */
    @GetMapping("/transportNewestRecordType")
    public WebResult<RecordTypeVO> transportNewestRecordType() {
        return WebResult.success(transportQuotedPriceService.transportNewestRecordType());
    }


    /**
     * 车获取某个货源自己的报价
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping(value = "/getCarToTransportQuotedPrice")
    public WebResult<TransportQuotedPriceCarVO> getCarToTransportQuotedPrice(@RequestParam("srcMsgId") Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId() || srcMsgId == null) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        return WebResult.success(transportQuotedPriceService.getCarToTransportQuotedPrice(loginUser.getUserId(), srcMsgId));
    }

    /**
     * 货主是否有货源被车方出价
     *
     * @return
     */
    @GetMapping(value = "/getTransportHaveAnyQuotedPrice")
    public WebResult<Boolean> getTransportHaveAnyQuotedPrice() {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        return WebResult.success(transportQuotedPriceService.getTransportHaveAnyQuotedPrice(loginUser.getUserId()));
    }

    /**
     * 车主是否存在货方回价了但车方还没想响应的货
     *
     * @return
     */
    @GetMapping(value = "/getCarHaveNewTransportQuotedPrice")
    public WebResult<Boolean> getCarHaveNewTransportQuotedPrice() {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        return WebResult.success(transportQuotedPriceService.getCarHaveNewTransportQuotedPrice(loginUser.getUserId()));
    }

    /**
     * 车离开货源详情报价挽留弹窗
     *
     * @return
     */
    @GetMapping(value = "/getCarLeaveTransportSingleDetailTabData")
    public WebResult<Boolean> getCarLeaveTransportSingleDetailTabData(@RequestParam("srcMsgId") Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        return WebResult.success(transportQuotedPriceService.getCarLeaveTransportSingleDetailTabData(loginUser.getUserId(), srcMsgId));
    }


    /**
     * 车离开货源详情报价挽留弹窗
     *
     * @return
     */
    @GetMapping(value = "/getTransportQuotedPricePageWord")
    public WebResult<String> getTransportQuotedPricePageWord(@RequestParam("srcMsgId") Long srcMsgId) {
        return WebResult.success(transportQuotedPriceService.getTransportQuotedPricePageWord(srcMsgId));
    }

    /**
     * 获取报价货源详情
     *
     * @return
     */
    @GetMapping(value = "/getTransportVO")
    public WebResult<TransportMainVO> getTransportVO(@RequestParam("srcMsgId") Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        return WebResult.success(transportQuotedPriceService.getTransportVO(srcMsgId, loginUser.getUserId()));
    }

    /**
     * 货获取某个货源的所有报价列表
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping(value = "/getTransportQuotedPriceList")
    public WebResult<List<TransportQuotedPriceTransportVO>> getTransportQuotedPriceList(@RequestParam("srcMsgId") Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId() || srcMsgId == null) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }

        return WebResult.success(transportQuotedPriceService.getTransportQuotedPriceList(srcMsgId, loginUser.getUserId()));
    }

    @GetMapping(value = "/carShowQuotedPriceBox")
    public WebResult<CarShowQuotedPriceBoxVO> carShowQuotedPriceBox(@RequestParam("srcMsgId") Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId() || srcMsgId == null) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }

        return WebResult.success(transportQuotedPriceService.carShowQuotedPriceBox(srcMsgId));
    }

}
