package com.teyuntong.goods.web.adapter.callphonerecord.controller;

import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.callphonerecord.vo.PrivacyPhoneNumGoodIdReq;
import com.teyuntong.goods.web.service.remote.callphonerecord.AxbRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/goods/privacyPhone")
@Slf4j
public class AxbController {

    private final AxbRemoteService axbRemoteService;

    @GetMapping("/getPrivacyPhoneTabInfo")
    @ResponseBody
    public WebResult getPrivacyPhoneTabInfo(@RequestParam(name="goodId",required = false) Long goodId
            , @RequestParam(name="carUserId",required = false) Long carUserId) {
        if (goodId == null) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_LACK, "请求参数错误");
        }
        if (carUserId == null) {
            LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
            carUserId = loginUser.getUserId();
        }
        return WebResult.success(axbRemoteService.getPrivacyPhoneTabInfo(goodId, carUserId));
    }

    @PostMapping("/getPrivacyPhoneNumCToT")
    @ResponseBody
    public WebResult getPrivacyPhoneNumCToT(@RequestBody PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        privacyPhoneNumGoodIdReq.setUserId(loginUser.getUserId());
        log.info("绑定虚拟号C2T请求参数：{}", JSONObject.toJSONString(privacyPhoneNumGoodIdReq));
        String privacyPhoneNumCToT = axbRemoteService.getPrivacyPhoneNumCToT(privacyPhoneNumGoodIdReq);
        return WebResult.success(privacyPhoneNumCToT);
    }

    @PostMapping("/getPrivacyPhoneNumTToC")
    @ResponseBody
    public WebResult getPrivacyPhoneNumTToC(@RequestBody PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        privacyPhoneNumGoodIdReq.setUserId(loginUser.getUserId());
        log.info("绑定虚拟号T2C请求参数：{}", JSONObject.toJSONString(privacyPhoneNumGoodIdReq));
        String privacyPhoneNumTToC = axbRemoteService.getPrivacyPhoneNumTToC(privacyPhoneNumGoodIdReq);
        return WebResult.success(privacyPhoneNumTToC);
    }

}
