package com.teyuntong.goods.web.adapter.transport.controller;

import com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailCanUseCountVO;
import com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailVO;
import com.teyuntong.goods.web.service.remote.goodsname.ExcellentGoodsRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/excellentGoods")
@RequiredArgsConstructor
@Slf4j
public class ExcellentGoodsController {

    private final ExcellentGoodsRemoteService excellentGoodsRemoteService;

    @GetMapping("/getAllNoUseCarListByUserId")
    public WebResult<List<TytExcellentGoodsCardUserDetailVO>> getAllNoUseCarListByUserId(@RequestParam("pageNum") Integer pageNum) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();

        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        return WebResult.success(excellentGoodsRemoteService.getAllNoUseCarListByUserId(loginUser.getUserId(), pageNum));
    }

    @GetMapping("/getAllCanUseCarCountNumByUserId")
    public WebResult<TytExcellentGoodsCardUserDetailCanUseCountVO> getAllCanUseCarCountNumByUserId() {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        return WebResult.success(excellentGoodsRemoteService.getAllCanUseCarCountNumByUserId(loginUser.getUserId()));
    }

    @GetMapping("/getAllCanUseCarListByUserId")
    public WebResult<List<TytExcellentGoodsCardUserDetailVO>> getAllCanUseCarListByUserId(@RequestParam("pageNum") Integer pageNum) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();

        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        return WebResult.success(excellentGoodsRemoteService.getAllCanUseCarListByUserId(loginUser.getUserId(), pageNum));
    }

}
