package com.teyuntong.goods.web.adapter.basic.controller;

import com.teyuntong.goods.service.client.navigation.dto.NavigationQueryDTO;
import com.teyuntong.goods.service.client.navigation.dto.NavigationTruckDTO;
import com.teyuntong.goods.service.client.navigation.dto.TruckNavigationInfoDTO;
import com.teyuntong.goods.service.client.navigation.vo.NavigationResultVO;
import com.teyuntong.goods.service.client.navigation.vo.NavigationTruckInfoVO;
import com.teyuntong.goods.web.service.remote.navigation.NavigationRemoteService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 导航控制器
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/navigation")
@Slf4j
public class NavigationController {

    private final NavigationRemoteService navigationService;

    /**
     * 查询货车导航距离
     */
    @PostMapping("/distance/truck")
    public WebResult<NavigationResultVO> truckDistance(@RequestBody @Validated NavigationQueryDTO navQueryDTO) {
        return WebResult.success(navigationService.navigationDistance(navQueryDTO));
    }

    /**
     * 根据吨重返回货车信息，用于导航
     */
    @GetMapping("/truck/info")
    public WebResult<NavigationTruckInfoVO> truckInfo(BigDecimal weight) {
        return WebResult.success(navigationService.navigationTruckInfo(weight));
    }

    /**
     * 查询货车导航
     */
    @PostMapping("/truck")
    public WebResult<String> navigationTruck(@RequestBody @Validated NavigationTruckDTO navigationTruckDTO) {
        return WebResult.success(navigationService.navigationTruck(navigationTruckDTO));
    }

    /**
     * 导航车辆信息上报
     */
    @PostMapping(value = "/truck/report")
    public WebResult<Boolean> reportNavigationTruck(@RequestBody TruckNavigationInfoDTO truckInfoDTO) {
        truckInfoDTO.setUserId(LoginHelper.getRequiredLoginUser().getUserId());
        return WebResult.success(navigationService.reportNavigationTruck(truckInfoDTO));
    }

    /**
     * 导航车辆信息删除
     */
    @GetMapping(value = "/truck/delete")
    public WebResult<Boolean> deleteNavigationTruck(@RequestParam(value = "navigateType") Integer navigateType) {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        return WebResult.success(navigationService.deleteNavigationTruck(userId, navigateType));
    }

    /**
     * 获取导航车辆信息
     */
    @GetMapping(value = "/truck/get")
    public WebResult<List<TruckNavigationInfoDTO>> getNavigationTruck(@RequestParam(value = "navigateType") Integer navigateType) {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        return WebResult.success(navigationService.getNavigationTruck(userId, navigateType));
    }
}
