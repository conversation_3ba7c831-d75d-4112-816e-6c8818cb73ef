package com.teyuntong.goods.web.adapter.callphonerecord.controller;

import com.teyuntong.goods.service.client.callphonerecord.vo.TransportRecordVo;
import com.teyuntong.goods.web.service.biz.callphonerecord.dto.CallPhoneRecordAddRemarkReq;
import com.teyuntong.goods.web.service.biz.callphonerecord.service.CallPhoneRecordService;
import com.teyuntong.goods.web.service.biz.quotedprice.service.TransportQuotedPriceService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/goods/callPhoneRecord")
public class CallPhoneRecordController {


    private final CallPhoneRecordService callPhoneRecordService;

    private final TransportQuotedPriceService transportQuotedPriceService;

    /**
     * Retrieves a list of transport records.
     * <p>
     * Method: GET
     * Endpoint: /transportList
     * <p>
     * This method is used to fetch the transport records associated with the logged-in user.
     * It first checks if a user is logged in and if their user ID is available. If not, an empty list is returned.
     * Otherwise, it calls the transportList() method of the callPhoneRecordService to retrieve the transport records
     * for the user and returns the result as a WebResult object.
     *
     * @return A WebResult object containing the list of TransportRecordVo objects if the user is logged in,
     * an empty list otherwise.
     */
    @GetMapping("/transportList")
    public WebResult<List<TransportRecordVo>> transportList() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success(List.of());
        }
        return WebResult.success(callPhoneRecordService.transportList(loginUser.getUserId()));
    }

    @GetMapping("/getTransportNoLookCallLogCountNum")
    public WebResult<Integer> getTransportNoLookOverQuotedPriceCountNum() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success(0);
        }
        //沟通记录未查看的货源总数加上未查看的报价总数
        int count = 0;
        Integer transportNoLookCallLogCountNum = callPhoneRecordService.getTransportNoLookCallLogCountNum(loginUser.getUserId());
        Integer transportNoLookOverQuotedPriceCountNum = transportQuotedPriceService.getTransportNoLookOverQuotedPriceCountNum(loginUser.getUserId());
        if (transportNoLookCallLogCountNum != null) {
            count += transportNoLookCallLogCountNum;
        }
        if (transportNoLookOverQuotedPriceCountNum != null) {
            count += transportNoLookOverQuotedPriceCountNum;
        }
        return WebResult.success(count);
    }

    @PostMapping("/addRemark")
    public WebResult addRemark(@RequestBody CallPhoneRecordAddRemarkReq callPhoneRecordAddRemarkReq) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success();
        }
        callPhoneRecordService.addRemark(callPhoneRecordAddRemarkReq);
        return WebResult.success();
    }

    @GetMapping("/getPhone")
    public WebResult getPhone(@RequestParam("linkUserId") Long linkUserId) {

        return WebResult.success(callPhoneRecordService.getPhone(linkUserId));
    }

    @GetMapping("/getPhoneNoAuth")
    public WebResult getPhoneNoAuth(@RequestParam("linkUserId") Long linkUserId) {
        return WebResult.success(callPhoneRecordService.getPhoneNoAuth(linkUserId));
    }


    /**
     * 意向车源列表
     * @param srcMsgId
     * @return
     */
    @GetMapping("/callList")
    WebResult contactedList(@RequestParam("srcMsgId") Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || null == loginUser.getUserId()) {
            return WebResult.success(List.of());
        }
        if (srcMsgId == null) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_LACK, "请求参数错误");
        }
        return WebResult.success(callPhoneRecordService.contactedList(srcMsgId, loginUser.getUserId()));

    }

}
