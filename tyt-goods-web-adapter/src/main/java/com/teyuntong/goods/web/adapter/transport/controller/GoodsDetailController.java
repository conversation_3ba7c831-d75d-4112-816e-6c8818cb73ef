package com.teyuntong.goods.web.adapter.transport.controller;

import com.teyuntong.goods.service.client.transport.dto.GoodsDiagnosisDTO;
import com.teyuntong.goods.service.client.transport.dto.ShowAddPriceDTO;
import com.teyuntong.goods.service.client.transport.dto.SingleDetailDTO;
import com.teyuntong.goods.web.service.common.error.TytGoodsWebErrorCode;
import com.teyuntong.goods.web.service.remote.goodsname.GoodsDetailRemoteService;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

import static com.teyuntong.goods.web.service.common.error.TytGoodsWebErrorCode.GOODS_ID_ERROR;
import static com.teyuntong.goods.web.service.common.error.TytGoodsWebErrorCode.PARAM_ERROR;

/**
 * 货源详情页接口
 *
 * <AUTHOR>
 * @since 2024/12/03 14:24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/goods/detail")
public class GoodsDetailController {

    private final GoodsDetailRemoteService goodsDetailRemoteService;

    /**
     * 货源详情
     *
     * <AUTHOR>
     * @date 2025/2/25 11:25
     * @param dto
     * @return WebResult
     */
    @PostMapping("/getSingleDetail")
    public WebResult getSingleDetail(@RequestBody SingleDetailDTO dto) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        dto.setUserId(loginUser.getUserId());
        if (Objects.nonNull(baseParam)) {
            dto.setClientSign(baseParam.getClientSign());
            dto.setClientVersion(baseParam.getClientVersion());
        }
        if (Objects.isNull(dto.getSrcMsgId()) || dto.getSrcMsgId() < 0) {
            log.warn("getSingleDetail 货源ID参数不合法:{}", dto.getSrcMsgId());
            return WebResult.error(GOODS_ID_ERROR);
        }
        if (Objects.isNull(dto.getDetailType()) || dto.getDetailType() < 1 || dto.getDetailType() > 5) {
            log.warn("getSingleDetail detailType参数不合法:{}", dto.getDetailType());
            return WebResult.error(PARAM_ERROR);
        }
        return WebResult.success(goodsDetailRemoteService.getSingleDetail(dto));
    }

    /**
     * 货源详情页货源诊断接口
     *
     * @param srcMsgId 货源id
     */
    @GetMapping(value = "/diagnosis")
    public WebResult goodsDiagnosis(@RequestParam("srcMsgId") Long srcMsgId) {
        GoodsDiagnosisDTO goodsDiagnosisDTO = goodsDetailRemoteService.goodsDiagnosis(srcMsgId);
        if (goodsDiagnosisDTO == null) {
            return WebResult.error(TytGoodsWebErrorCode.GOODS_STATUS_ERROR);
        }
        // 版本6580以下不显示货源诊断的加价功能
        String clientVersion = LoginHelper.getBaseParam().getClientVersion();
        if (StringUtils.isNotBlank(clientVersion) && StringUtils.isNumeric(clientVersion) && Integer.parseInt(clientVersion) < 6580) {
            if (CollectionUtils.isNotEmpty(goodsDiagnosisDTO.getTaskList())) {
                goodsDiagnosisDTO.getTaskList().removeIf(task -> task.getType() == 2);
            }
        }

        if (StringUtils.isNotBlank(clientVersion) && StringUtils.isNumeric(clientVersion) && Integer.parseInt(clientVersion) < 6640) {
            if (CollectionUtils.isNotEmpty(goodsDiagnosisDTO.getTaskList())) {
                goodsDiagnosisDTO.getTaskList().removeIf(task -> task.getType() == 5);
            }
        }

        return WebResult.success(goodsDiagnosisDTO);
    }

    /**
     * 货源详情是否展示加价自动弹窗，进入货源详情页弹
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping(value = "/showAddPrice")
    public WebResult showAddPrice(@RequestParam("srcMsgId") Long srcMsgId) {
        ShowAddPriceDTO priceDTO = goodsDetailRemoteService.showAddPrice(srcMsgId);
        return WebResult.success(priceDTO);
    }
}
