package com.teyuntong.goods.web.adapter.transport.controller;

import com.teyuntong.goods.service.client.transport.dto.SameTransportAvgPriceQueryRpcDTO;
import com.teyuntong.goods.service.client.transport.vo.SameTransportAvgPriceResultVO;
import com.teyuntong.goods.web.service.remote.thprice.SameTransportAvgPriceRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sameTransportAvgPrice")
public class SameTransportAvgPriceController {

    private final SameTransportAvgPriceRemoteService sameTransportAvgPriceRemoteService;

    /**
     * 获取某个货源的相似货源成交价，如果用户id不为null，则额外查询当前用户下的相似货源成交价
     *
     * @param srcMsgId 货源id
     */
    @GetMapping(value = "/getBySrcMsgIdAndUserId")
    WebResult getSameTransportAvgPrice(@RequestParam("srcMsgId") Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == null) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_LACK, "请求参数错误");
        }
        return WebResult.success(sameTransportAvgPriceRemoteService.getSameTransportAvgPrice(srcMsgId, loginUser.getUserId()));
    }

    /**
     * 获取相似货源成交价
     */
    @PostMapping(value = "/getByQuery")
    WebResult<SameTransportAvgPriceResultVO> getSameTransportAvgPrice(@RequestBody @Validated SameTransportAvgPriceQueryRpcDTO queryDTO) {
        return WebResult.success(sameTransportAvgPriceRemoteService.getSameTransportAvgPrice(queryDTO));
    }

    /**
     * 获取货源近x天相似货源成交价
     *
     * @param srcMsgId 货源id
     * @param days     天数
     * @return 平均成交价
     */
    @GetMapping(value = "/getByDays")
    WebResult<BigDecimal> getSameTransportAvgPriceByDays(@RequestParam("srcMsgId") Long srcMsgId, @RequestParam("days") int days) {
        return WebResult.success(sameTransportAvgPriceRemoteService.getSameTransportAvgPriceByDays(srcMsgId, days));
    }

}
