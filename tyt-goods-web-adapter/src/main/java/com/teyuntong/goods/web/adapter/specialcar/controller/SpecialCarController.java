package com.teyuntong.goods.web.adapter.specialcar.controller;

import com.teyuntong.goods.service.client.specialcar.dto.SpecialCarDTO;
import com.teyuntong.goods.web.service.remote.specialcar.SpecialCarRemoteService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/06/30 17:34
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/specialCar")
public class SpecialCarController {

    private final SpecialCarRemoteService specialCarRemoteService;

    @PostMapping(value = "/save")
    public WebResult<Bo<PERSON>an> saveSpecialCar(@RequestBody SpecialCarDTO specialCarDTO){
        specialCarRemoteService.saveSpecialCar(specialCarDTO);
        return WebResult.success();
    }
}
