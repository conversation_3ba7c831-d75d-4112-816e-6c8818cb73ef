package com.teyuntong.goods.web.adapter.transport.controller;

import com.teyuntong.goods.service.client.transport.dto.GoodCarPriceTransportTabAndBIPriceDTO;
import com.teyuntong.goods.service.client.transport.dto.GoodCarPriceUserLeftDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportCarryDTO;
import com.teyuntong.goods.web.service.remote.goodsname.GoodCarPriceTransportRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/goods/goodCarPriceTransport")
@Slf4j
public class GoodCarPriceTransportController {

    private final GoodCarPriceTransportRemoteService goodCarPriceTransportRemoteService;

    /**
     * 是否展示优车运价货源卡片
     */
    @PostMapping(value = "/isShowGoodCarPriceTransportTab")
    @ResponseBody
    public WebResult<GoodCarPriceTransportTabAndBIPriceDTO> isShowGoodCarPriceTransportTab(@RequestBody @Validated TransportCarryDTO transportCarryDTO) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            GoodCarPriceTransportTabAndBIPriceDTO goodCarPriceTransportTabAndBIPriceDTO = new GoodCarPriceTransportTabAndBIPriceDTO();
            goodCarPriceTransportTabAndBIPriceDTO.setShowTab(false);
            return WebResult.success(goodCarPriceTransportTabAndBIPriceDTO);
        }
        transportCarryDTO.setUserId(loginUser.getUserId());
        GoodCarPriceTransportTabAndBIPriceDTO showGoodCarPriceTransportTab = goodCarPriceTransportRemoteService.isShowGoodCarPriceTransportTab(transportCarryDTO);
        return WebResult.success(showGoodCarPriceTransportTab);
    }

    /**
     * 优车2.0剩余次数
     */
    @GetMapping(value = "/showLeftTimes")
    public WebResult<GoodCarPriceUserLeftDTO> showLeftTimes() {
        return WebResult.success(goodCarPriceTransportRemoteService.showLeftTimes());
    }

}
