package ${package.ServiceImpl};

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * ${table.comment!} 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */

@Service
<#if kotlin>
open class ${table.serviceImplName} : <#if table.serviceInterface>, ${table.serviceName}</#if> {

}
<#else>
 @Slf4j
 @RequiredArgsConstructor
public class ${table.serviceImplName}<#if table.serviceInterface> implements ${table.serviceName}</#if> {

}
</#if>
